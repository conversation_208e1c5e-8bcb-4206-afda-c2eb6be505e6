// assets
import { IconUsers, IconSearch, IconDiscount, IconUserPlus } from '@tabler/icons-react';

// constant
const icons = {
  IconUsers,
  IconSearch,
  IconDiscount,
  IconUserPlus
};

// ==============================|| CLIENT MANAGEMENT MENU ITEMS ||============================== //

const clientManagement = {
  id: 'client-management',
  title: 'Client Management',
  type: 'group',
  children: [
    {
      id: 'clients',
      title: 'Clients',
      type: 'item',
      url: '/clients/list',
      icon: icons.IconUsers,
      breadcrumbs: false
    },
    {
      id: 'client-groups',
      title: 'Client Groups',
      type: 'item',
      url: '/clients/groups',
      icon: icons.IconUserPlus,
      breadcrumbs: false
    },
    {
      id: 'client-discount-profiles',
      title: 'Discount Profiles',
      type: 'item',
      url: '/client-discount-profiles',
      icon: icons.IconDiscount,
      breadcrumbs: false
    }
  ]
};

export default clientManagement;
