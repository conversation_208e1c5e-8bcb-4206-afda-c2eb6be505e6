// assets
import { IconPhoto, IconLayoutDashboard, IconBrandPagekit, IconHome } from '@tabler/icons-react';

// constant
const icons = {
  IconPhoto,
  IconLayoutDashboard,
  IconBrandPagekit,
  IconHome
};

// ==============================|| CONTENT MANAGEMENT MENU ITEMS ||============================== //

const contentManagement = {
  id: 'content-management',
  title: 'Gestion du Contenu',
  type: 'group',
  children: [
    {
      id: 'carousels',
      title: 'Carousels',
      type: 'item',
      url: '/carousels',
      icon: icons.IconPhoto,
      breadcrumbs: false
    },
    {
      id: 'homepage',
      title: "Page d'accueil",
      type: 'item',
      url: '/homepage',
      icon: icons.IconHome,
      breadcrumbs: false
    },
    {
      id: 'pages',
      title: 'Pages',
      type: 'item',
      url: '/pages',
      icon: icons.IconBrandPagekit,
      breadcrumbs: false
    },
    {
      id: 'layouts',
      title: 'Mises en page',
      type: 'item',
      url: '/layouts',
      icon: icons.IconLayoutDashboard,
      breadcrumbs: false
    }
  ]
};

export default contentManagement;
