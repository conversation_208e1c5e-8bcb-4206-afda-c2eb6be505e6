import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, Col, <PERSON>, Button, Form, Table, Alert, Spinner, <PERSON>dal, Badge, InputGroup, Tabs, Tab } from 'react-bootstrap';
import { FaPlus, FaPencilAlt, FaTrashAlt, FaUsers, FaSearch, FaFilter, FaSort, FaEye, FaBuilding, FaPercent } from 'react-icons/fa';
import {
  fetchPartners,
  createPartner,
  updatePartner,
  deletePartner,
  fetchPartnerPointsOfSale,
  assignPointOfSaleToPartner,
  removePointOfSaleFromPartner,
  fetchAllPointsOfSale
} from '../../services/partnerService';
import ImageManager from '../GestionCommerciale/ImageManager';

const PartnerManagement = () => {
  // State for partners
  const [partners, setPartners] = useState([]);
  const [partnerForm, setPartnerForm] = useState({
    name: '',
    description: '',
    contact_name: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    city: '',
    postal_code: '',
    country: '',
    is_active: true
  });
  const [editingPartnerId, setEditingPartnerId] = useState(null);
  const [partnerLoading, setPartnerLoading] = useState(false);
  const [partnerSubmitting, setPartnerSubmitting] = useState(false);

  // State for points of sale
  const [selectedPartnerId, setSelectedPartnerId] = useState(null);
  const [pointsOfSale, setPointsOfSale] = useState([]);
  const [allPointsOfSale, setAllPointsOfSale] = useState([]);
  const [pointOfSaleLoading, setPointOfSaleLoading] = useState(false);
  const [selectedPointOfSaleId, setSelectedPointOfSaleId] = useState('');

  // UI state
  const [activeTab, setActiveTab] = useState('list');
  const [showPartnerModal, setShowPartnerModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [partnerToDelete, setPartnerToDelete] = useState(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredPartners, setFilteredPartners] = useState([]);

  // Load partners
  const loadPartners = async () => {
    setPartnerLoading(true);
    setError('');
    try {
      const data = await fetchPartners();
      setPartners(data);
      setFilteredPartners(data);
    } catch (e) {
      setError(`Error loading partners: ${e.message}`);
    }
    setPartnerLoading(false);
  };

  // Load points of sale for a partner
  const loadPartnerPointsOfSale = async (partnerId) => {
    setPointOfSaleLoading(true);
    setError('');
    try {
      const [partnerPos, allPos] = await Promise.all([fetchPartnerPointsOfSale(partnerId), fetchAllPointsOfSale()]);
      setPointsOfSale(partnerPos);
      setAllPointsOfSale(allPos);
      setSelectedPartnerId(partnerId);
      setActiveTab('points-of-sale');
    } catch (e) {
      setError(`Error loading points of sale: ${e.message}`);
    }
    setPointOfSaleLoading(false);
  };

  // Initial data load
  useEffect(() => {
    loadPartners();
  }, []);

  // Filter partners when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredPartners(partners);
    } else {
      const filtered = partners.filter(
        (partner) =>
          partner.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (partner.description && partner.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (partner.contact_name && partner.contact_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (partner.contact_email && partner.contact_email.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (partner.city && partner.city.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredPartners(filtered);
    }
  }, [searchTerm, partners]);

  // Handle partner form changes
  const handlePartnerChange = (e) => {
    const { name, value, type, checked } = e.target;
    setPartnerForm({
      ...partnerForm,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Submit partner form
  const handlePartnerSubmit = async () => {
    if (!partnerForm.name) {
      setError('Partner name is required');
      return;
    }

    setPartnerSubmitting(true);
    setError('');

    try {
      if (editingPartnerId) {
        await updatePartner(editingPartnerId, partnerForm);
        setSuccess('Partner updated successfully');
      } else {
        await createPartner(partnerForm);
        setSuccess('Partner created successfully');
      }

      setShowPartnerModal(false);
      resetPartnerForm();
      loadPartners();
    } catch (e) {
      setError(`Error saving partner: ${e.message}`);
    }

    setPartnerSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Reset partner form
  const resetPartnerForm = () => {
    setPartnerForm({
      name: '',
      description: '',
      contact_name: '',
      contact_email: '',
      contact_phone: '',
      address: '',
      city: '',
      postal_code: '',
      country: '',
      is_active: true
    });
    setEditingPartnerId(null);
  };

  // Edit partner
  const handleEditPartner = (partner) => {
    setPartnerForm({
      name: partner.name,
      description: partner.description || '',
      contact_name: partner.contact_name || '',
      contact_email: partner.contact_email || '',
      contact_phone: partner.contact_phone || '',
      address: partner.address || '',
      city: partner.city || '',
      postal_code: partner.postal_code || '',
      country: partner.country || '',
      is_active: partner.is_active
    });
    setEditingPartnerId(partner.id);
    setShowPartnerModal(true);
  };

  // Confirm delete
  const confirmDeletePartner = (partner) => {
    setPartnerToDelete(partner);
    setShowDeleteModal(true);
  };

  // Handle delete
  const handleDeletePartner = async () => {
    setError('');

    try {
      await deletePartner(partnerToDelete.id);
      loadPartners();
      setSuccess('Partner deleted successfully');
    } catch (e) {
      setError(`Error deleting partner: ${e.message}`);
    }

    setShowDeleteModal(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Assign point of sale to partner
  const handleAssignPointOfSale = async () => {
    if (!selectedPointOfSaleId) {
      setError('Please select a point of sale');
      return;
    }

    setError('');
    try {
      await assignPointOfSaleToPartner(selectedPartnerId, selectedPointOfSaleId);
      setSuccess('Point of sale assigned successfully');
      loadPartnerPointsOfSale(selectedPartnerId);
      setSelectedPointOfSaleId('');
    } catch (e) {
      setError(`Error assigning point of sale: ${e.message}`);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  // Remove point of sale from partner
  const handleRemovePointOfSale = async (pointOfSaleId) => {
    if (!window.confirm('Are you sure you want to remove this point of sale from the partner?')) {
      return;
    }

    setError('');
    try {
      await removePointOfSaleFromPartner(selectedPartnerId, pointOfSaleId);
      setSuccess('Point of sale removed successfully');
      loadPartnerPointsOfSale(selectedPartnerId);
    } catch (e) {
      setError(`Error removing point of sale: ${e.message}`);
    }

    setTimeout(() => setSuccess(''), 3000);
  };

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="mb-4">
        <h2 className="fw-bold text-primary mb-2">
          <FaUsers className="me-2" />
          Partner Management
        </h2>
        <p className="text-muted">Manage partners, their information, and associated points of sale.</p>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="list"
              title={
                <span>
                  <FaUsers className="me-2" />
                  Partners
                </span>
              }
            />
            <Tab
              eventKey="points-of-sale"
              title={
                <span>
                  <FaBuilding className="me-2" />
                  Points of Sale
                </span>
              }
              disabled={!selectedPartnerId}
            />
            <Tab
              eventKey="discounts"
              title={
                <span>
                  <FaPercent className="me-2" />
                  Discounts
                </span>
              }
              disabled={!selectedPartnerId}
            />
            <Tab
              eventKey="images"
              title={
                <span>
                  <FaEye className="me-2" />
                  Images
                </span>
              }
              disabled={!selectedPartnerId}
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Tab Content */}
      {activeTab === 'list' && (
        <>
          {/* Search and Actions */}
          <Card className="shadow-sm mb-4 border-0">
            <Card.Body>
              <Row className="align-items-center">
                <Col md={6}>
                  <InputGroup>
                    <InputGroup.Text>
                      <FaSearch />
                    </InputGroup.Text>
                    <Form.Control placeholder="Search partners..." value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
                  </InputGroup>
                </Col>
                <Col md={6} className="text-md-end mt-3 mt-md-0">
                  <Button
                    variant="primary"
                    onClick={() => {
                      resetPartnerForm();
                      setShowPartnerModal(true);
                    }}
                  >
                    <FaPlus className="me-2" />
                    Add New Partner
                  </Button>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Partners List */}
          <Card className="shadow-sm border-0">
            <Card.Body className="p-0">
              {partnerLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Loading partners...</p>
                </div>
              ) : filteredPartners.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaUsers style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">No partners found.</p>
                  <Button
                    variant="primary"
                    onClick={() => {
                      resetPartnerForm();
                      setShowPartnerModal(true);
                    }}
                  >
                    <FaPlus className="me-2" />
                    Create First Partner
                  </Button>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead className="bg-light">
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Contact</th>
                      <th>Location</th>
                      <th>Status</th>
                      <th>Points of Sale</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredPartners.map((partner) => (
                      <tr key={partner.id}>
                        <td>{partner.id}</td>
                        <td>
                          <div className="fw-medium">{partner.name}</div>
                          {partner.description && (
                            <div className="text-muted small text-truncate" style={{ maxWidth: '200px' }}>
                              {partner.description}
                            </div>
                          )}
                        </td>
                        <td>
                          {partner.contact_name && <div>{partner.contact_name}</div>}
                          {partner.contact_email && <div className="small">{partner.contact_email}</div>}
                          {partner.contact_phone && <div className="small">{partner.contact_phone}</div>}
                        </td>
                        <td>
                          {partner.city && <div>{partner.city}</div>}
                          {partner.country && <div className="small">{partner.country}</div>}
                        </td>
                        <td>{partner.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                        <td>
                          <Badge bg="info">{partner.points_of_sale_count || 0} locations</Badge>
                        </td>
                        <td>
                          <Button size="sm" variant="outline-info" className="me-1" onClick={() => loadPartnerPointsOfSale(partner.id)}>
                            <FaBuilding className="me-1" /> Points of Sale
                          </Button>
                          <Button size="sm" variant="outline-primary" className="me-1" onClick={() => handleEditPartner(partner)}>
                            <FaPencilAlt className="me-1" /> Edit
                          </Button>
                          <Button size="sm" variant="outline-danger" onClick={() => confirmDeletePartner(partner)}>
                            <FaTrashAlt className="me-1" /> Delete
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'points-of-sale' && (
        <>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div>
              <h5 className="mb-0">Points of Sale for Partner: {partners.find((p) => p.id === selectedPartnerId)?.name}</h5>
            </div>
            <Button variant="outline-secondary" onClick={() => setActiveTab('list')}>
              Back to Partners
            </Button>
          </div>

          {/* Assign Point of Sale */}
          <Card className="shadow-sm mb-4 border-0">
            <Card.Header className="bg-white py-3">
              <h6 className="mb-0 fw-bold">Assign Point of Sale</h6>
            </Card.Header>
            <Card.Body>
              <Row className="align-items-end">
                <Col md={8}>
                  <Form.Group>
                    <Form.Label>Select Point of Sale</Form.Label>
                    <Form.Select value={selectedPointOfSaleId} onChange={(e) => setSelectedPointOfSaleId(e.target.value)}>
                      <option value="">Select a point of sale</option>
                      {allPointsOfSale
                        .filter((pos) => !pointsOfSale.some((p) => p.id === pos.id))
                        .map((pos) => (
                          <option key={pos.id} value={pos.id}>
                            {pos.name} - {pos.address}, {pos.city}
                          </option>
                        ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
                <Col md={4}>
                  <Button variant="primary" onClick={handleAssignPointOfSale} disabled={!selectedPointOfSaleId} className="w-100">
                    <FaPlus className="me-2" />
                    Assign
                  </Button>
                </Col>
              </Row>
            </Card.Body>
          </Card>

          {/* Points of Sale List */}
          <Card className="shadow-sm border-0">
            <Card.Header className="bg-white py-3 d-flex justify-content-between align-items-center">
              <h6 className="mb-0 fw-bold">Assigned Points of Sale</h6>
              <Badge bg="info">{pointsOfSale.length} locations</Badge>
            </Card.Header>
            <Card.Body className="p-0">
              {pointOfSaleLoading ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                  <p className="mt-3 text-muted">Loading points of sale...</p>
                </div>
              ) : pointsOfSale.length === 0 ? (
                <div className="text-center py-5">
                  <div className="mb-3">
                    <FaStore style={{ fontSize: '3rem' }} className="text-muted" />
                  </div>
                  <p className="text-muted">No points of sale assigned to this partner.</p>
                </div>
              ) : (
                <Table hover responsive className="align-middle mb-0">
                  <thead className="bg-light">
                    <tr>
                      <th>ID</th>
                      <th>Name</th>
                      <th>Address</th>
                      <th>City</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {pointsOfSale.map((pos) => (
                      <tr key={pos.id}>
                        <td>{pos.id}</td>
                        <td>
                          <span className="fw-medium">{pos.name}</span>
                        </td>
                        <td>{pos.address}</td>
                        <td>{pos.city}</td>
                        <td>{pos.is_active ? <Badge bg="success">Active</Badge> : <Badge bg="secondary">Inactive</Badge>}</td>
                        <td>
                          <Button size="sm" variant="outline-danger" onClick={() => handleRemovePointOfSale(pos.id)}>
                            <FaTrashAlt className="me-1" /> Remove
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              )}
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'images' && (
        <>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div>
              <h5 className="mb-0">Images for Partner: {partners.find((p) => p.id === selectedPartnerId)?.name}</h5>
            </div>
            <Button variant="outline-secondary" onClick={() => setActiveTab('list')}>
              Back to Partners
            </Button>
          </div>

          <Card className="shadow-sm border-0">
            <Card.Body>
              <ImageManager modelType="partner" modelId={selectedPartnerId} />
            </Card.Body>
          </Card>
        </>
      )}

      {activeTab === 'discounts' && (
        <>
          <div className="d-flex justify-content-between align-items-center mb-3">
            <div>
              <h5 className="mb-0">Discounts for Partner: {partners.find((p) => p.id === selectedPartnerId)?.name}</h5>
            </div>
            <Button variant="outline-secondary" onClick={() => setActiveTab('list')}>
              Back to Partners
            </Button>
          </div>

          <Card className="shadow-sm border-0">
            <Card.Body className="text-center py-5">
              <div className="mb-3">
                <FaPercentage style={{ fontSize: '3rem' }} className="text-muted" />
              </div>
              <p className="text-muted">Partner discount management will be implemented in the next phase.</p>
            </Card.Body>
          </Card>
        </>
      )}

      {/* Partner Modal */}
      <Modal show={showPartnerModal} onHide={() => setShowPartnerModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{editingPartnerId ? 'Edit Partner' : 'Add New Partner'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Partner Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="name"
                    value={partnerForm.name}
                    onChange={handlePartnerChange}
                    placeholder="Enter partner name"
                    required
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    label="Active"
                    checked={partnerForm.is_active}
                    onChange={handlePartnerChange}
                  />
                </Form.Group>
              </Col>
            </Row>
            <Form.Group className="mb-3">
              <Form.Label>Description</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                name="description"
                value={partnerForm.description}
                onChange={handlePartnerChange}
                placeholder="Enter partner description"
              />
            </Form.Group>
            <h6 className="mb-3">Contact Information</h6>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Name</Form.Label>
                  <Form.Control
                    type="text"
                    name="contact_name"
                    value={partnerForm.contact_name}
                    onChange={handlePartnerChange}
                    placeholder="Enter contact name"
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Email</Form.Label>
                  <Form.Control
                    type="email"
                    name="contact_email"
                    value={partnerForm.contact_email}
                    onChange={handlePartnerChange}
                    placeholder="Enter contact email"
                  />
                </Form.Group>
              </Col>
            </Row>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Contact Phone</Form.Label>
                  <Form.Control
                    type="text"
                    name="contact_phone"
                    value={partnerForm.contact_phone}
                    onChange={handlePartnerChange}
                    placeholder="Enter contact phone"
                  />
                </Form.Group>
              </Col>
            </Row>
            <h6 className="mb-3">Address</h6>
            <Form.Group className="mb-3">
              <Form.Label>Address</Form.Label>
              <Form.Control
                type="text"
                name="address"
                value={partnerForm.address}
                onChange={handlePartnerChange}
                placeholder="Enter address"
              />
            </Form.Group>
            <Row>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>City</Form.Label>
                  <Form.Control type="text" name="city" value={partnerForm.city} onChange={handlePartnerChange} placeholder="Enter city" />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Postal Code</Form.Label>
                  <Form.Control
                    type="text"
                    name="postal_code"
                    value={partnerForm.postal_code}
                    onChange={handlePartnerChange}
                    placeholder="Enter postal code"
                  />
                </Form.Group>
              </Col>
              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Country</Form.Label>
                  <Form.Control
                    type="text"
                    name="country"
                    value={partnerForm.country}
                    onChange={handlePartnerChange}
                    placeholder="Enter country"
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPartnerModal(false)}>
            Cancel
          </Button>
          <Button variant="primary" onClick={handlePartnerSubmit} disabled={partnerSubmitting}>
            {partnerSubmitting ? (
              <>
                <Spinner size="sm" animation="border" className="me-2" />
                Processing...
              </>
            ) : (
              'Save Partner'
            )}
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal show={showDeleteModal} onHide={() => setShowDeleteModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Confirm Delete</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          Are you sure you want to delete the partner "{partnerToDelete?.name}"?
          {partnerToDelete?.points_of_sale_count > 0 && (
            <Alert variant="warning" className="mt-3">
              <strong>Warning:</strong> This partner has {partnerToDelete.points_of_sale_count} points of sale assigned. Deleting this
              partner will remove these associations.
            </Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleDeletePartner}>
            Delete
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default PartnerManagement;
