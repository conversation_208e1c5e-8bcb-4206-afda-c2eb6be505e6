import { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Checkbox from '@mui/material/Checkbox';
import FormControl from '@mui/material/FormControl';
import FormControlLabel from '@mui/material/FormControlLabel';
import Grid from '@mui/material/Grid2';
import IconButton from '@mui/material/IconButton';
import InputAdornment from '@mui/material/InputAdornment';
import InputLabel from '@mui/material/InputLabel';
import OutlinedInput from '@mui/material/OutlinedInput';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';
import CircularProgress from '@mui/material/CircularProgress';

// project imports
import AnimateButton from 'ui-component/extended/AnimateButton';
import { useAuth } from '../../../contexts/AuthContext';
import keycloakService from '../../../utils/keycloak';

// assets
import Visibility from '@mui/icons-material/Visibility';
import VisibilityOff from '@mui/icons-material/VisibilityOff';

// ===============================|| JWT - LOGIN ||=============================== //

export default function AuthLogin() {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { login, error, isLoading, clearError } = useAuth();

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [checked, setChecked] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [loginError, setLoginError] = useState('');

  // Check for messages from registration or other sources
  const registrationMessage = location.state?.message;

  const handleClickShowPassword = () => {
    setShowPassword(!showPassword);
  };

  const handleMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
    // Clear errors when user starts typing
    if (loginError) setLoginError('');
    if (error) clearError();
  };

  // Handle form submission
  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!formData.email || !formData.password) {
      setLoginError('Please enter both email and password');
      return;
    }

    try {
      // For demo purposes, we'll simulate Keycloak authentication
      // In a real implementation, this would redirect to Keycloak
      const tokens = await keycloakService.login();

      // Verify tokens with backend
      await login(tokens);

      // Redirect to intended page or dashboard
      const from = location.state?.from || '/dashboard/default';
      navigate(from, { replace: true });
    } catch (error) {
      console.error('Login failed:', error);
      setLoginError(error.message || 'Login failed. Please try again.');
    }
  };

  // Handle Keycloak login button
  const handleKeycloakLogin = async () => {
    try {
      const tokens = await keycloakService.login();
      await login(tokens);

      const from = location.state?.from || '/dashboard/default';
      navigate(from, { replace: true });
    } catch (error) {
      console.error('Keycloak login failed:', error);
      setLoginError('Authentication failed. Please try again.');
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      {/* Registration Message Display */}
      {registrationMessage && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {registrationMessage}
        </Alert>
      )}

      {/* Error Display */}
      {(error || loginError) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || loginError}
        </Alert>
      )}

      <FormControl fullWidth sx={{ ...theme.typography.customInput }}>
        <InputLabel htmlFor="outlined-adornment-email-login">Email Address / Username</InputLabel>
        <OutlinedInput
          id="outlined-adornment-email-login"
          type="email"
          value={formData.email}
          name="email"
          onChange={handleInputChange}
          inputProps={{}}
        />
      </FormControl>

      <FormControl fullWidth sx={{ ...theme.typography.customInput }}>
        <InputLabel htmlFor="outlined-adornment-password-login">Password</InputLabel>
        <OutlinedInput
          id="outlined-adornment-password-login"
          type={showPassword ? 'text' : 'password'}
          value={formData.password}
          name="password"
          onChange={handleInputChange}
          endAdornment={
            <InputAdornment position="end">
              <IconButton
                aria-label="toggle password visibility"
                onClick={handleClickShowPassword}
                onMouseDown={handleMouseDownPassword}
                edge="end"
                size="large"
              >
                {showPassword ? <Visibility /> : <VisibilityOff />}
              </IconButton>
            </InputAdornment>
          }
          inputProps={{}}
          label="Password"
        />
      </FormControl>

      <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
        <Grid>
          <FormControlLabel
            control={<Checkbox checked={checked} onChange={(event) => setChecked(event.target.checked)} name="checked" color="primary" />}
            label="Keep me logged in"
          />
        </Grid>
        <Grid>
          <Typography variant="subtitle1" component={Link} to="/forgot-password" color="secondary" sx={{ textDecoration: 'none' }}>
            Forgot Password?
          </Typography>
        </Grid>
      </Grid>

      <Box sx={{ mt: 2 }}>
        <AnimateButton>
          <Button
            color="secondary"
            fullWidth
            size="large"
            type="submit"
            variant="contained"
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isLoading ? 'Signing In...' : 'Sign In'}
          </Button>
        </AnimateButton>
      </Box>

      {/* Keycloak Login Button */}
      <Box sx={{ mt: 2 }}>
        <AnimateButton>
          <Button color="primary" fullWidth size="large" variant="outlined" onClick={handleKeycloakLogin} disabled={isLoading}>
            Login with Keycloak
          </Button>
        </AnimateButton>
      </Box>
    </form>
  );
}
