import { useState } from 'react';
import { useLocation } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';

// project imports
import AnimateButton from 'ui-component/extended/AnimateButton';
import { useAuth } from '../../../contexts/AuthContext';
import { redirectToKeycloakLogin } from '../../../utils/keycloak';

// ===============================|| JWT - LOGIN ||=============================== //

export default function AuthLogin() {
  const theme = useTheme();
  const location = useLocation();
  const { error } = useAuth();

  const [loginError, setLoginError] = useState('');

  // Check for messages from registration or other sources
  const registrationMessage = location.state?.message;

  // Handle Keycloak login redirect
  const handleKeycloakLogin = () => {
    try {
      console.log('🔐 Login button clicked');

      // Store the intended destination
      const from = location.state?.from || '/app/dashboard/default';
      sessionStorage.setItem('auth_redirect_url', from);

      console.log('🔐 Stored redirect URL:', from);
      console.log('🔐 Calling redirectToKeycloakLogin...');

      // Redirect to Keycloak login
      redirectToKeycloakLogin();
    } catch (error) {
      console.error('🔐 Keycloak login redirect failed:', error);
      setLoginError('Failed to redirect to login. Please try again.');
    }
  };

  return (
    <Box>
      {/* Registration Message Display */}
      {registrationMessage && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {registrationMessage}
        </Alert>
      )}

      {/* Error Display */}
      {(error || loginError) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || loginError}
        </Alert>
      )}

      {/* Keycloak Login Button */}
      <Box sx={{ mt: 2 }}>
        <AnimateButton>
          <Button
            color="primary"
            fullWidth
            size="large"
            variant="contained"
            onClick={handleKeycloakLogin}
            sx={{ py: 1.5, fontSize: '1.1rem' }}
          >
            Sign In
          </Button>
        </AnimateButton>
      </Box>

      {/* Information */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          Secure authentication via Keycloak
        </Typography>
      </Box>
    </Box>
  );
}
