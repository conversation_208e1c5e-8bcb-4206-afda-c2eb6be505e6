import { useState } from 'react';
import { useLocation } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';

// project imports
import AnimateButton from 'ui-component/extended/AnimateButton';
import { useAuth } from '../../../contexts/AuthContext';
import { redirectToKeycloakLogin } from '../../../utils/keycloak';
import authService from '../../../services/authService';

// ===============================|| JWT - LOGIN ||=============================== //

export default function AuthLogin() {
  const theme = useTheme();
  const location = useLocation();
  const { error } = useAuth();

  const [loginError, setLoginError] = useState('');

  // Check for messages from registration or other sources
  const registrationMessage = location.state?.message;

  // Handle Keycloak login redirect
  const handleKeycloakLogin = () => {
    try {
      console.log('🔐 Login button clicked');

      // Check if user is already authenticated
      const isAuth = authService.isAuthenticated();
      console.log('🔐 Current auth status:', isAuth);

      if (isAuth) {
        console.log('🔐 User already authenticated, clearing data first...');
        authService.clearAllAuthData();
        // Reload page to reset auth state
        window.location.reload();
        return;
      }

      // Store the intended destination
      const from = location.state?.from || '/app/dashboard/default';
      sessionStorage.setItem('auth_redirect_url', from);

      console.log('🔐 Stored redirect URL:', from);
      console.log('🔐 Calling redirectToKeycloakLogin...');

      // Redirect to Keycloak login
      redirectToKeycloakLogin();
    } catch (error) {
      console.error('🔐 Keycloak login redirect failed:', error);
      setLoginError('Failed to redirect to login. Please try again.');
    }
  };

  // Clear all authentication data
  const handleClearAuthData = () => {
    console.log('🔐 Manually clearing authentication data...');
    authService.clearAllAuthData();
    setLoginError('');
    alert('Authentication data cleared! You can now test the login flow.');
  };

  // Check current auth status
  const handleCheckAuthStatus = () => {
    const isAuth = authService.isAuthenticated();
    const user = authService.getUser();
    const tokens = {
      access: !!authService.getAccessToken(),
      refresh: !!authService.getRefreshToken(),
      id: !!authService.getIdToken()
    };

    console.log('🔐 Current Authentication Status:', {
      isAuthenticated: isAuth,
      user: user,
      hasTokens: tokens
    });

    alert(`Auth Status: ${isAuth ? 'Authenticated' : 'Not Authenticated'}\nCheck console for details.`);
  };

  return (
    <Box>
      {/* Registration Message Display */}
      {registrationMessage && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {registrationMessage}
        </Alert>
      )}

      {/* Error Display */}
      {(error || loginError) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || loginError}
        </Alert>
      )}

      {/* Keycloak Login Button */}
      <Box sx={{ mt: 2 }}>
        <AnimateButton>
          <Button
            color="primary"
            fullWidth
            size="large"
            variant="contained"
            onClick={handleKeycloakLogin}
            sx={{ py: 1.5, fontSize: '1.1rem' }}
          >
            Sign In
          </Button>
        </AnimateButton>
      </Box>

      {/* Information */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          Secure authentication via Keycloak
        </Typography>
      </Box>

      {/* Debug Section */}
      <Box sx={{ mt: 3, p: 2, border: '1px dashed #ccc', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          🔧 Debug Tools
        </Typography>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
          <Button size="small" variant="outlined" color="warning" onClick={handleClearAuthData}>
            Clear Auth Data
          </Button>
          <Button size="small" variant="outlined" color="info" onClick={handleCheckAuthStatus}>
            Check Auth Status
          </Button>
        </Box>
        <Typography variant="caption" color="textSecondary" sx={{ mt: 1, display: 'block' }}>
          Use these tools to debug the authentication flow
        </Typography>
      </Box>
    </Box>
  );
}
