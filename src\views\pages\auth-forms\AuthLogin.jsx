import { useState } from 'react';
import { useLocation } from 'react-router-dom';

// material-ui
import { useTheme } from '@mui/material/styles';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Alert from '@mui/material/Alert';

// project imports
import AnimateButton from 'ui-component/extended/AnimateButton';
import { useAuth } from '../../../contexts/AuthContext';
import { redirectToKeycloakLogin } from '../../../utils/keycloak';

// ===============================|| JWT - LOGIN ||=============================== //

export default function AuthLogin() {
  const theme = useTheme();
  const location = useLocation();
  const { error } = useAuth();

  const [loginError, setLoginError] = useState('');

  // Check for messages from registration or other sources
  const registrationMessage = location.state?.message;

  // Handle Keycloak login redirect
  const handleKeycloakLogin = () => {
    try {
      console.log('🔐 Login button clicked');

      // Store the intended destination
      const from = location.state?.from || '/app/dashboard/default';
      sessionStorage.setItem('auth_redirect_url', from);

      console.log('🔐 Stored redirect URL:', from);
      console.log('🔐 Calling redirectToKeycloakLogin...');

      // Redirect to Keycloak login
      redirectToKeycloakLogin();
    } catch (error) {
      console.error('🔐 Keycloak login redirect failed:', error);
      setLoginError('Failed to redirect to login. Please try again.');
    }
  };

  return (
    <Box>
      {/* Registration Message Display */}
      {registrationMessage && (
        <Alert severity="info" sx={{ mb: 2 }}>
          {registrationMessage}
        </Alert>
      )}

      {/* Error Display */}
      {(error || loginError) && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || loginError}
        </Alert>
      )}

      {/* Welcome Message */}
      <Box sx={{ textAlign: 'center', mb: 3 }}>
        <Typography variant="h4" gutterBottom>
          Welcome to Jihene-Line Backoffice
        </Typography>
        <Typography variant="body1" color="textSecondary">
          Please sign in with your Keycloak account to continue
        </Typography>
      </Box>

      {/* Keycloak Login Button */}
      <Box sx={{ mt: 3 }}>
        <AnimateButton>
          <Button color="primary" fullWidth size="large" variant="contained" onClick={handleKeycloakLogin} sx={{ py: 1.5 }}>
            Sign In with Keycloak
          </Button>
        </AnimateButton>
      </Box>

      {/* Information */}
      <Box sx={{ mt: 3, textAlign: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          You will be redirected to the secure Keycloak login page
        </Typography>
      </Box>

      {/* Help Text */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Typography variant="body2" color="textSecondary">
          Don't have an account? Contact your administrator for access.
        </Typography>
      </Box>

      {/* Debug Link */}
      <Box sx={{ mt: 2, textAlign: 'center' }}>
        <Typography variant="body2">
          <a href="/keycloak-test" style={{ color: '#1976d2', textDecoration: 'none' }}>
            🔧 Test Keycloak Configuration
          </a>
        </Typography>
      </Box>
    </Box>
  );
}
