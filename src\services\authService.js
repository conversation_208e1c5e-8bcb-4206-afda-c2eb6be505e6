const API_URL = 'https://laravel-api.fly.dev/api';

// Authentication Service
export class AuthService {
  constructor() {
    this.tokenKey = 'access_token';
    this.refreshTokenKey = 'refresh_token';
    this.userKey = 'user_data';
  }

  // Verify Keycloak token with the backend
  async verifyToken(tokens) {
    try {
      console.log('🔐 Attempting to verify token with backend...');

      const response = await fetch(`${API_URL}/auth/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          id_token: tokens.id_token
        }),
        credentials: 'include', // Include cookies for session management
        timeout: 10000 // 10 second timeout
      });

      if (!response.ok) {
        throw new Error(`Authentication failed: ${response.status}`);
      }

      const data = await response.json();

      // Store tokens and user data
      this.setTokens(tokens);
      this.setUser(data.user);

      console.log('🔐 Backend verification successful');
      return {
        success: true,
        user: data.user,
        roles: data.roles,
        message: data.message
      };
    } catch (error) {
      console.error('🔐 Backend verification failed:', error);

      // Fallback: Extract user info from tokens for development
      console.log('🔐 Using fallback token verification...');
      return this.fallbackTokenVerification(tokens);
    }
  }

  // Fallback verification when backend is unavailable
  async fallbackTokenVerification(tokens) {
    try {
      console.log('🔐 Extracting user data from Keycloak tokens...');

      // Try to decode the ID token first (contains most user info), then access token
      let userInfo = null;

      if (tokens.id_token && tokens.id_token !== 'fallback_id') {
        console.log('🔐 Decoding ID token...');
        userInfo = this.decodeJWTToken(tokens.id_token);
      } else if (tokens.access_token && tokens.access_token !== 'fallback_token') {
        console.log('🔐 Decoding access token...');
        userInfo = this.decodeJWTToken(tokens.access_token);
      }

      if (!userInfo) {
        console.log('🔐 No valid tokens found, using minimal fallback data');
        throw new Error('No valid token data available');
      }

      console.log('🔐 Extracted token payload:', userInfo);

      // Extract user information from the real Keycloak token
      const user = {
        id: userInfo.sub,
        keycloak_id: userInfo.sub,
        name: userInfo.name ||
              `${userInfo.given_name || ''} ${userInfo.family_name || ''}`.trim() ||
              userInfo.preferred_username ||
              'User',
        email: userInfo.email || userInfo.preferred_username || '<EMAIL>',
        roles: this.extractRolesFromToken(userInfo)
      };

      // Store tokens and user data
      this.setTokens(tokens);
      this.setUser(user);

      console.log('🔐 Fallback verification successful with real user data:', user);

      return {
        success: true,
        user: user,
        roles: user.roles,
        message: 'Authenticated via fallback (backend unavailable) - using real Keycloak data'
      };
    } catch (error) {
      console.error('🔐 Fallback verification failed:', error);

      // Last resort: create minimal user data
      console.log('🔐 Creating minimal fallback user...');
      const minimalUser = {
        id: 'fallback-user',
        keycloak_id: 'fallback-user',
        name: 'Authenticated User',
        email: '<EMAIL>',
        roles: ['client']
      };

      this.setTokens(tokens);
      this.setUser(minimalUser);

      return {
        success: true,
        user: minimalUser,
        roles: minimalUser.roles,
        message: 'Authenticated via minimal fallback'
      };
    }
  }

  // Decode JWT token
  decodeJWTToken(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
        return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
      }).join(''));

      const payload = JSON.parse(jsonPayload);

      // Debug: Log the available user fields
      console.log('🔐 Available user fields in token:', {
        sub: payload.sub,
        name: payload.name,
        given_name: payload.given_name,
        family_name: payload.family_name,
        preferred_username: payload.preferred_username,
        email: payload.email,
        email_verified: payload.email_verified,
        realm_access: payload.realm_access,
        resource_access: payload.resource_access
      });

      return payload;
    } catch (error) {
      console.error('Failed to decode JWT token:', error);
      throw error;
    }
  }

  // Extract roles from token payload
  extractRolesFromToken(tokenPayload) {
    const roles = [];

    // Extract realm roles
    if (tokenPayload.realm_access && tokenPayload.realm_access.roles) {
      roles.push(...tokenPayload.realm_access.roles);
    }

    // Extract client roles
    if (tokenPayload.resource_access) {
      Object.values(tokenPayload.resource_access).forEach(clientAccess => {
        if (clientAccess.roles) {
          roles.push(...clientAccess.roles);
        }
      });
    }

    // Ensure 'client' role is always present
    if (!roles.includes('client')) {
      roles.push('client');
    }

    return [...new Set(roles)]; // Remove duplicates
  }

  // Logout user
  async logout() {
    try {
      const response = await fetch(`${API_URL}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include' // Include cookies for session management
      });

      if (!response.ok) {
        console.warn('Logout request failed, but clearing local data anyway');
      }

      // Clear local storage regardless of API response
      this.clearAuthData();

      return { success: true };
    } catch (error) {
      console.error('Logout failed:', error);
      // Clear local data even if API call fails
      this.clearAuthData();
      throw error;
    }
  }

  // Refresh authentication token
  async refreshToken() {
    try {
      const refreshToken = this.getRefreshToken();
      if (!refreshToken) {
        throw new Error('No refresh token available');
      }

      const response = await fetch(`${API_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: refreshToken
        }),
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Token refresh failed: ${response.status}`);
      }

      const data = await response.json();

      // Update stored tokens
      this.setTokens({
        access_token: data.access_token,
        refresh_token: data.refresh_token || refreshToken,
        id_token: data.id_token
      });

      return data;
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.clearAuthData();
      throw error;
    }
  }

  // Get current user info
  async getCurrentUser() {
    try {
      const response = await fetch(`${API_URL}/auth/user`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.getAccessToken()}`
        },
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error(`Failed to get user info: ${response.status}`);
      }

      const userData = await response.json();
      this.setUser(userData);
      return userData;
    } catch (error) {
      console.error('Failed to get current user:', error);
      throw error;
    }
  }

  // Token management methods
  setTokens(tokens) {
    if (tokens.access_token) {
      localStorage.setItem(this.tokenKey, tokens.access_token);
    }
    if (tokens.refresh_token) {
      localStorage.setItem(this.refreshTokenKey, tokens.refresh_token);
    }
  }

  getAccessToken() {
    return localStorage.getItem(this.tokenKey);
  }

  getRefreshToken() {
    return localStorage.getItem(this.refreshTokenKey);
  }

  setUser(userData) {
    localStorage.setItem(this.userKey, JSON.stringify(userData));
  }

  getUser() {
    const userData = localStorage.getItem(this.userKey);
    return userData ? JSON.parse(userData) : null;
  }

  clearAuthData() {
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.refreshTokenKey);
    localStorage.removeItem(this.userKey);
  }

  // Check if user is authenticated
  isAuthenticated() {
    const token = this.getAccessToken();
    const user = this.getUser();
    return !!(token && user);
  }

  // Check if token is expired (basic check)
  isTokenExpired() {
    const token = this.getAccessToken();
    if (!token) return true;

    try {
      // Decode JWT token to check expiration
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Date.now() / 1000;
      return payload.exp < currentTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  }

  // Get user roles
  getUserRoles() {
    const user = this.getUser();
    return user?.roles || [];
  }

  // Check if user has specific role
  hasRole(role) {
    const roles = this.getUserRoles();
    return roles.includes(role);
  }

  // Check if user has any of the specified roles
  hasAnyRole(roles) {
    const userRoles = this.getUserRoles();
    return roles.some(role => userRoles.includes(role));
  }

  // Clear all authentication data (for debugging)
  clearAllAuthData() {
    console.log('🔐 Clearing all authentication data...');
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('id_token');
    localStorage.removeItem('user_data');
    sessionStorage.removeItem('auth_redirect_url');
    console.log('🔐 All authentication data cleared');
  }

  // Registration request (for backoffice applications)
  async submitRegistrationRequest(userData) {
    try {
      // In a real implementation, this might send an email to administrators
      // or create a pending user record that requires approval

      console.log('Registration request submitted:', userData);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // For now, just return success
      // In production, this would integrate with Keycloak Admin API or send notifications
      return {
        success: true,
        message: 'Registration request submitted successfully. An administrator will review your request.'
      };
    } catch (error) {
      console.error('Registration request failed:', error);
      throw new Error('Failed to submit registration request. Please try again.');
    }
  }
}

// Create singleton instance
const authService = new AuthService();
export default authService;
