// assets
import { IconClipboardList, IconList, IconTags, IconFile } from '@tabler/icons-react';

// constant
const icons = {
  IconClipboardList,
  IconList,
  IconTags,
  IconFile
};

// ==============================|| ORDER MANAGEMENT MENU ITEMS ||============================== //

const orderManagement = {
  id: 'order-management',
  title: 'Order Management',
  type: 'group',
  children: [
    {
      id: 'orders',
      title: 'Orders',
      type: 'collapse',
      icon: icons.IconClipboardList,
      children: [
        {
          id: 'order-list',
          title: 'Order List',
          type: 'item',
          url: '/orders/list',
          breadcrumbs: false
        },
        {
          id: 'order-statuses',
          title: 'Order Statuses',
          type: 'item',
          url: '/order-statuses',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'invoices',
      title: 'Invoices',
      type: 'item',
      url: '/invoices',
      icon: icons.IconFile,
      breadcrumbs: false
    }
  ]
};

export default orderManagement;
