// assets
import { IconPackage, IconShoppingCart, IconUsers } from '@tabler/icons-react';

// constant
const icons = {
  IconPackage,
  IconShoppingCart,
  IconUsers
};

// ==============================|| GESTION DES DONNÉES MENU ITEMS ||============================== //

const gestionDonnees = {
  id: 'gestion-donnees',
  title: 'Gestion des Données',
  type: 'group',
  children: [
    {
      id: 'util-typography',
      title: 'Produits',
      type: 'item',
      url: '/typography',
      icon: icons.IconPackage,
      breadcrumbs: false
    },
    {
      id: 'util-color',
      title: 'Commandes',
      type: 'item',
      url: '/color',
      icon: icons.IconShoppingCart,
      breadcrumbs: false
    },
    {
      id: 'util-shadow',
      title: 'Clients',
      type: 'item',
      url: '/shadow',
      icon: icons.IconUsers,
      breadcrumbs: false
    }
  ]
};

export default gestionDonnees;
