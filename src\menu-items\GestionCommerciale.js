// assets
import {
  IconTags, // pour Collections
  IconDiscount, // pour Promotions
  IconCategory, // pour Catégories
  IconListDetails // pour Attributs
} from '@tabler/icons-react';

// constant
const icons = {
  IconTags,
  IconDiscount,
  IconCategory,
  IconListDetails
};

// ==============================|| GESTION COMMERCIALE MENU ITEMS ||============================== //

const GestionCommerciale = {
  id: 'GestionCommerciale',
  title: 'Gestion Commerciale',
  type: 'group',
  children: [
    {
      id: 'categories',
      title: 'Catégories',
      type: 'collapse',
      icon: icons.IconCategory,
      children: [
        {
          id: 'categories-management',
          title: 'Gestion des catégories',
          type: 'item',
          url: '/categories',
          breadcrumbs: false
        },
        {
          id: 'featured-categories',
          title: 'Catégories mises en avant',
          type: 'item',
          url: '/categories#featured',
          breadcrumbs: false
        }
      ]
    },
    {
      id: 'attributes',
      title: 'Attributs',
      type: 'item',
      url: '/attributes',
      icon: icons.IconListDetails,
      breadcrumbs: false
    },
    {
      id: 'list-collections',
      title: 'Collections',
      type: 'item',
      url: '/LCollection', // Liste des collections
      icon: icons.IconTags,
      breadcrumbs: false
    },
    {
      id: 'list-promotions',
      title: 'Promotions',
      type: 'item',
      url: '/Lpromotions', // Liste des promotions
      icon: icons.IconDiscount,
      breadcrumbs: false
    }
  ]
};

export default GestionCommerciale;
