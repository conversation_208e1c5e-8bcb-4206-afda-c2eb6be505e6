import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>ton, Row, Col, Al<PERSON>, Badge, Modal, Table, Image, Spinner, Tab, Tabs } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import axios from 'axios';
import { FaUpload, FaTrash, FaStar, FaRegStar, FaPlus } from 'react-icons/fa';

const API_URL = 'https://laravel-api.fly.dev/api';

const AjoutCat = () => {
  // Styles
  const colors = {
    primary: '#1a237e',
    secondary: '#283593',
    accent: '#3949ab',
    light: '#e8eaf6',
    dark: '#0d47a1',
    success: '#28a745',
    info: '#17a2b8',
    warning: '#ffc107',
    danger: '#dc3545'
  };

  const fontStyle = {
    fontFamily: "'Montserrat', sans-serif",
    fontWeight: 500
  };

  // États
  const [tabValue, setTabValue] = useState('category');
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [error, setError] = useState(null);
  const [successMessage, setSuccessMessage] = useState(null);
  const [uploadedImages, setUploadedImages] = useState([]);

  // Formulaires
  const [category, setCategory] = useState({
    nom: '',
    description: '',
    image: null
  });

  const [subCategory, setSubCategory] = useState({
    nom: '',
    description: '',
    categorie_id: ''
  });

  // Charger les données initiales
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const [categoriesRes, subCategoriesRes] = await Promise.all([
          axios.get(`${API_URL}/categories`),
          axios.get(`${API_URL}/sousCategories`)
        ]);
        setCategories(categoriesRes.data);
        setSubCategories(subCategoriesRes.data);
      } catch (err) {
        console.error('Erreur:', err);
        setError('Erreur de chargement des données. Veuillez rafraîchir la page.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Gestion des erreurs/succès
  const handleApiError = (err) => {
    console.error("Détails de l'erreur:", err.response?.data);

    let errorMessage = 'Erreur lors de la création';
    const apiError = err.response?.data;

    if (apiError?.errors) {
      errorMessage = Object.entries(apiError.errors)
        .map(([field, messages]) => {
          const fieldNames = {
            nom: 'Nom',
            description: 'Description',
            categorie_id: 'Catégorie parente'
          };

          return `• ${fieldNames[field] || field}: ${messages.join(', ')}`;
        })
        .join('\n');
    } else if (apiError?.message) {
      errorMessage = apiError.message;
    }

    setError(errorMessage);
  };

  // Gestion des images
  const handleImageUpload = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('image', file);
    formData.append('model_type', 'categorie');
    formData.append('alt_text', `Image pour ${category.nom || 'nouvelle catégorie'}`);
    formData.append('title', category.nom || 'Nouvelle catégorie');

    try {
      setUploading(true);
      const response = await axios.post(`${API_URL}/images/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      setCategory((prev) => ({ ...prev, image: response.data.image.id }));
      setUploadedImages([response.data.image]);
      setSuccessMessage('Image téléchargée avec succès');
    } catch (err) {
      handleApiError(err);
    } finally {
      setUploading(false);
    }
  };

  const handleDeleteImage = async (imageId) => {
    try {
      await axios.delete(`${API_URL}/images/${imageId}`);
      setUploadedImages([]);
      setCategory((prev) => ({ ...prev, image: null }));
      setSuccessMessage('Image supprimée avec succès');
    } catch (err) {
      handleApiError(err);
    }
  };

  // Gestion des formulaires
  const handleCategorySubmit = async (e) => {
    e.preventDefault();

    if (!category.nom) {
      setError('Le nom de la catégorie est obligatoire');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/categories`, {
        ...category,
        image_id: category.image || null
      });

      setCategories([...categories, response.data]);
      setCategory({ nom: '', description: '', image: null });
      setUploadedImages([]);
      setSuccessMessage('Catégorie créée avec succès!');
    } catch (err) {
      handleApiError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubCategorySubmit = async (e) => {
    e.preventDefault();

    if (!subCategory.nom || !subCategory.categorie_id) {
      setError('Le nom et la catégorie parente sont obligatoires');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.post(`${API_URL}/sousCategories`, subCategory);

      setSubCategories([...subCategories, response.data]);
      setSubCategory({ nom: '', description: '', categorie_id: '' });
      setSuccessMessage('Sous-catégorie créée avec succès!');
    } catch (err) {
      handleApiError(err);
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (setter) => (e) => {
    const { name, value } = e.target;
    setter((prev) => ({ ...prev, [name]: value }));
  };

  if (loading && categories.length === 0) {
    return (
      <Container className="py-5 text-center">
        <Spinner animation="border" style={{ color: colors.primary }} />
        <p className="mt-3">Chargement en cours...</p>
      </Container>
    );
  }

  return (
    <Container className="py-4" style={fontStyle}>
      {/* En-tête */}
      <div className="text-center mb-5">
        <h1 style={{ color: colors.primary }}>GESTION DES CATÉGORIES</h1>
        <div
          style={{
            height: '3px',
            width: '120px',
            background: `linear-gradient(90deg, rgba(26, 35, 126, 0.2) 0%, ${colors.primary} 50%, rgba(26, 35, 126, 0.2) 100%)`,
            borderRadius: '3px',
            margin: '0 auto'
          }}
        ></div>
      </div>

      {/* Messages d'erreur/succès */}
      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          <div style={{ whiteSpace: 'pre-wrap' }}>
            <i className="fas fa-exclamation-triangle me-2"></i>
            {error}
          </div>
        </Alert>
      )}

      {successMessage && (
        <Alert variant="success" onClose={() => setSuccessMessage(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {successMessage}
        </Alert>
      )}

      {/* Onglets */}
      <Tabs activeKey={tabValue} onSelect={(k) => setTabValue(k)} className="mb-4" style={{ borderBottom: `2px solid ${colors.light}` }}>
        <Tab eventKey="category" title="Ajouter Catégorie" />
        <Tab eventKey="subcategory" title="Ajouter Sous-catégorie" />
      </Tabs>

      {/* Formulaire Catégorie */}
      {tabValue === 'category' && (
        <Card className="shadow-sm mb-4">
          <Card.Body>
            <Form onSubmit={handleCategorySubmit}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Nom de la catégorie *</Form.Label>
                    <Form.Control type="text" name="nom" value={category.nom} onChange={handleInputChange(setCategory)} required />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      name="description"
                      value={category.description}
                      onChange={handleInputChange(setCategory)}
                      rows={3}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Image de la catégorie</Form.Label>

                    {uploadedImages.length > 0 ? (
                      <div className="position-relative mb-3">
                        <Image
                          src={`${API_URL}/images/serve/${uploadedImages[0].id}`}
                          fluid
                          rounded
                          style={{ maxHeight: '200px', width: '100%', objectFit: 'cover' }}
                        />
                        <div className="position-absolute top-0 end-0 p-2 bg-dark bg-opacity-50 rounded-bottom-start">
                          <Button variant="link" className="text-white me-2" onClick={() => {}} disabled={uploading}>
                            <FaRegStar size={20} />
                          </Button>
                          <Button
                            variant="link"
                            className="text-danger"
                            onClick={() => handleDeleteImage(uploadedImages[0].id)}
                            disabled={uploading}
                          >
                            <FaTrash size={20} />
                          </Button>
                        </div>
                        <Badge bg="success" className="position-absolute top-0 start-0 m-2">
                          Principale
                        </Badge>
                      </div>
                    ) : (
                      <div className="border rounded p-4 text-center" style={{ borderStyle: 'dashed' }}>
                        <input type="file" id="image-upload" accept="image/*" onChange={handleImageUpload} style={{ display: 'none' }} />
                        <label htmlFor="image-upload" className="d-flex flex-column align-items-center">
                          {uploading ? (
                            <Spinner animation="border" variant="primary" />
                          ) : (
                            <>
                              <FaUpload size={40} className="mb-3" style={{ color: colors.primary }} />
                              <p>Glissez-déposez ou cliquez pour télécharger</p>
                              <p className="text-muted small">Format: JPG, PNG (max 10MB)</p>
                              <Button variant="outline-primary" className="mt-2">
                                Sélectionner une image
                              </Button>
                            </>
                          )}
                        </label>
                      </div>
                    )}
                  </Form.Group>

                  <div className="d-grid">
                    <Button variant="primary" type="submit" disabled={loading || !category.nom}>
                      {loading ? <Spinner as="span" size="sm" animation="border" className="me-2" /> : <i className="fas fa-save me-2"></i>}
                      Enregistrer la catégorie
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          </Card.Body>
        </Card>
      )}

      {/* Formulaire Sous-catégorie */}
      {tabValue === 'subcategory' && (
        <Card className="shadow-sm mb-4">
          <Card.Body>
            <Form onSubmit={handleSubCategorySubmit}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Nom de la sous-catégorie *</Form.Label>
                    <Form.Control type="text" name="nom" value={subCategory.nom} onChange={handleInputChange(setSubCategory)} required />
                  </Form.Group>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      name="description"
                      value={subCategory.description}
                      onChange={handleInputChange(setSubCategory)}
                      rows={3}
                    />
                  </Form.Group>
                </Col>

                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Catégorie parente *</Form.Label>
                    <Form.Select
                      name="categorie_id"
                      value={subCategory.categorie_id}
                      onChange={handleInputChange(setSubCategory)}
                      required
                      disabled={categories.length === 0}
                    >
                      <option value="">Sélectionnez une catégorie</option>
                      {categories.map((cat) => (
                        <option key={`cat-${cat.id}`} value={cat.id}>
                          {cat.nom}
                        </option>
                      ))}
                    </Form.Select>
                    {categories.length === 0 && (
                      <Form.Text className="text-danger">Aucune catégorie disponible. Veuillez d'abord créer une catégorie.</Form.Text>
                    )}
                  </Form.Group>

                  <div className="d-grid">
                    <Button variant="primary" type="submit" disabled={loading || !subCategory.nom || !subCategory.categorie_id}>
                      {loading ? <Spinner as="span" size="sm" animation="border" className="me-2" /> : <i className="fas fa-save me-2"></i>}
                      Enregistrer la sous-catégorie
                    </Button>
                  </div>
                </Col>
              </Row>
            </Form>
          </Card.Body>
        </Card>
      )}
    </Container>
  );
};

export default AjoutCat;
