// assets
import { IconBuildingCommunity, IconUsers, IconDiscount, IconBuilding } from '@tabler/icons-react';

// constant
const icons = {
  IconBuildingCommunity,
  IconUsers,
  IconDiscount,
  IconBuilding
};

// ==============================|| PARTNER MANAGEMENT MENU ITEMS ||============================== //

const partnerManagement = {
  id: 'partner-management',
  title: 'Partner Management',
  type: 'group',
  children: [
    {
      id: 'partners',
      title: 'Partners',
      type: 'item',
      url: '/partners',
      icon: icons.IconUsers,
      breadcrumbs: false
    },
    {
      id: 'points-of-sale',
      title: 'Points of Sale',
      type: 'item',
      url: '/points-of-sale',
      icon: icons.IconBuilding,
      breadcrumbs: false
    },
    {
      id: 'partner-discount-profiles',
      title: 'Discount Profiles',
      type: 'item',
      url: '/partner-discount-profiles',
      icon: icons.IconDiscount,
      breadcrumbs: false
    }
  ]
};

export default partnerManagement;
