import PropTypes from 'prop-types';
import { useState, useEffect } from 'react';

// material-ui
import { Box, Tab, Tabs, Typography, Button } from '@mui/material';
import { IconUserPlus } from '@tabler/icons-react';

// project imports
import MainCard from 'ui-component/cards/MainCard';

// assets

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div role="tabpanel" hidden={value !== index} id={`client-tabpanel-${index}`} aria-labelledby={`client-tab-${index}`} {...other}>
      {value === index && (
        <Box sx={{ p: 3 }}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.number.isRequired,
  value: PropTypes.number.isRequired
};

function a11yProps(index) {
  return {
    id: `client-tab-${index}`,
    'aria-controls': `client-tabpanel-${index}`
  };
}

// ==============================|| CLIENT LIST ||============================== //

const ClientList = () => {
  const [value, setValue] = useState(0);
  const [isLoading, setLoading] = useState(true);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {
    setLoading(false);
  }, []);

  return (
    <MainCard
      title="Client Management"
      secondary={
        <Button variant="contained" startIcon={<IconUserPlus />}>
          Add Client
        </Button>
      }
    >
      <Box sx={{ width: '100%' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={value} onChange={handleChange} aria-label="client list tabs">
            <Tab label="All Clients" {...a11yProps(0)} />
            <Tab label="Active Clients" {...a11yProps(1)} />
            <Tab label="Inactive Clients" {...a11yProps(2)} />
            {/* Add more tabs as needed, e.g., By Group */}
          </Tabs>
        </Box>
        <TabPanel value={value} index={0}>
          <Typography variant="h5" gutterBottom>
            All Clients
          </Typography>
          <Typography>
            Placeholder for all clients list. This section will display a table or list of all clients with details like name, email,
            registration date, total orders, etc. Actions like view details, edit client, manage groups, etc., will be available.
          </Typography>
        </TabPanel>
        <TabPanel value={value} index={1}>
          <Typography variant="h5" gutterBottom>
            Active Clients
          </Typography>
          <Typography>Placeholder for clients who are currently active or have recent activity.</Typography>
        </TabPanel>
        <TabPanel value={value} index={2}>
          <Typography variant="h5" gutterBottom>
            Inactive Clients
          </Typography>
          <Typography>Placeholder for clients who have not been active for a certain period.</Typography>
        </TabPanel>
      </Box>
    </MainCard>
  );
};

export default ClientList;
