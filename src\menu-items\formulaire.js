// assets
import {
  IconPackage,
  IconDiscount, // pour Promotion
  IconTags, // pour Collection
  IconPlus, // pour Ajout Produits (optionnel)
  IconCalendarEvent, // pour Event
  IconCategory // pour Categorie
} from '@tabler/icons-react';

// constant
const icons = {
  IconPackage,
  IconDiscount,
  IconTags,
  IconPlus,
  IconCalendarEvent,
  IconCategory
};

// ==============================|| GESTION DES DONNÉES MENU ITEMS ||============================== //

const formulaire = {
  id: 'formulaire',
  title: 'Forms',
  type: 'group',
  children: [
    {
      id: 'util-typography',
      title: 'Ajout Produits',
      type: 'item',
      url: '/AjoutProduit',
      icon: icons.IconPlus, // ou IconPackage si tu préfères
      breadcrumbs: false
    },
    {
      id: 'util-color',
      title: 'Collection',
      type: 'item',
      url: '/Collection',
      icon: icons.IconTags,
      breadcrumbs: false
    },
    {
      id: 'util-shadow',
      title: 'Promotion',
      type: 'item',
      url: '/Promotion',
      icon: icons.IconDiscount,
      breadcrumbs: false
    },
    {
      id: 'event-item',
      title: 'Event',
      type: 'item',
      url: '/PromotionEvent',
      icon: icons.IconCalendarEvent,
      breadcrumbs: false
    },
    {
      id: 'category-item',
      title: 'Categorie',
      type: 'item',
      url: '/AjoutCat',
      icon: icons.IconCategory,
      breadcrumbs: false
    }
  ]
};

export default formulaire;
