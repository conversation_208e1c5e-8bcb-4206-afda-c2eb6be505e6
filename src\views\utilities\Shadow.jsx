import React, { useState, useEffect } from 'react';
import { Tabs, Tab, Card, Container, Row, Col, Button, Form, Badge, Modal, Pagination } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';

const fontStyle = {
  fontFamily: "'Montserrat', sans-serif",
  fontWeight: 500
};

const colors = {
  primaryDark: '#2a3f5f',
  primaryLight: '#3a537b',
  accent: '#3a8dde',
  partner: '#e74c3c',
  loyal: '#f39c12',
  regular: '#2ecc71',
  architect: '#8e44ad',
  hotel: '#16a085',
  jline: '#3498db',
  location: '#27ae60'
};

const ClientManagement = () => {
  // États
  const [activeTab, setActiveTab] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(6);
  const [showModal, setShowModal] = useState(false);
  const [modalAction, setModalAction] = useState('add');
  const [currentClient, setCurrentClient] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    category: 'regular',
    partnerType: '',
    company: '',
    location: 'Tunis',
    sellerGroup: '',
    sellerBrand: ''
  });

  const [clients, setClients] = useState([
    {
      id: 1,
      name: 'Chaima Bentaher',
      email: '<EMAIL>',
      phone: '+216 55 123 456',
      category: 'partner',
      partnerType: 'architect',
      company: 'Bentaher Architectes',
      lastPurchase: '2023-11-15',
      totalPurchases: 6,
      location: 'Sfax',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 2,
      name: 'Mohamed Bouaziz',
      email: '<EMAIL>',
      phone: '+216 22 987 654',
      category: 'partner',
      partnerType: 'architect',
      company: 'Bouaziz Architectes',
      lastPurchase: '2023-12-02',
      totalPurchases: 8,
      location: 'Sfax',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 3,
      name: 'Sirine Khalfaoui',
      email: '<EMAIL>',
      phone: '+216 98 765 432',
      category: 'regular',
      partnerType: '',
      company: '',
      lastPurchase: '2023-10-20',
      totalPurchases: 2,
      location: 'Monastir',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 4,
      name: 'Chaima Chelly',
      email: '<EMAIL>',
      phone: '+216 54 321 987',
      category: 'partner',
      partnerType: 'hotel',
      company: 'Hôtel Méditerranée',
      lastPurchase: '2023-11-28',
      totalPurchases: 15,
      location: 'Hammamet',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 5,
      name: 'Youssef Ben Ahmed',
      email: '<EMAIL>',
      phone: '+216 21 654 987',
      category: 'loyal',
      partnerType: '',
      company: 'Design Intérieur',
      lastPurchase: '2023-12-10',
      totalPurchases: 5,
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 6,
      name: 'Islem Trigui',
      email: '<EMAIL>',
      phone: '+216 92 876 543',
      category: 'regular',
      partnerType: '',
      company: '',
      lastPurchase: '2023-09-15',
      totalPurchases: 3,
      location: 'Sousse',
      sellerGroup: '',
      sellerBrand: ''
    },
    {
      id: 7,
      name: 'Ji-line Tunis',
      email: '<EMAIL>',
      phone: '+216 71 123 456',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-12-15',
      totalPurchases: 25,
      location: 'Tunis',
      sellerGroup: 'Ji-line GROUPE',
      sellerBrand: 'Luxoria'
    },
    {
      id: 8,
      name: 'Ji-line Sfax',
      email: '<EMAIL>',
      phone: '+216 74 987 654',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-12-10',
      totalPurchases: 18,
      location: 'Sfax',
      sellerGroup: 'Ji-line GROUPE',
      sellerBrand: 'Modernio'
    },
    {
      id: 9,
      name: 'Ji-line Monastir',
      email: '<EMAIL>',
      phone: '+216 73 456 789',
      category: 'partner',
      partnerType: '',
      company: '',
      lastPurchase: '2023-11-25',
      totalPurchases: 12,
      location: 'Monastir',
      sellerGroup: 'Ji-line GROUPE',
      sellerBrand: 'Eleganto'
    },
    {
      id: 10,
      name: 'hoshos chelly',
      email: '<EMAIL>',
      phone: '+216 22 987 654',
      category: 'loyal',
      partnerType: '',
      company: 'Déco Moderne',
      lastPurchase: '2023-12-02',
      totalPurchases: 8,
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    }
  ]);

  // Filtrer les clients selon l'onglet actif et la recherche
  const filteredClients = clients.filter((client) => {
    const matchesCategory =
      activeTab === 'all' || (activeTab === 'jline' ? client.sellerGroup === 'Ji-line GROUPE' : activeTab === client.category);

    const matchesSearch =
      searchTerm === '' ||
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (client.company && client.company.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.location && client.location.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.sellerGroup && client.sellerGroup.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (client.sellerBrand && client.sellerBrand.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesCategory && matchesSearch;
  });

  // Pagination
  const indexOfLastClient = currentPage * itemsPerPage;
  const indexOfFirstClient = indexOfLastClient - itemsPerPage;
  const currentClients = filteredClients.slice(indexOfFirstClient, indexOfLastClient);
  const totalPages = Math.ceil(filteredClients.length / itemsPerPage);

  // Obtenir le libellé de la catégorie
  const getCategoryLabel = (category) => {
    switch (category) {
      case 'partner':
        return 'Partenaire';
      case 'loyal':
        return 'Client fidèle';
      case 'regular':
        return 'Client simple';
      default:
        return '';
    }
  };

  // Obtenir la couleur de la catégorie
  const getCategoryColor = (category, partnerType = '', sellerGroup = '') => {
    if (sellerGroup === 'Ji-line GROUPE') return colors.jline;

    if (category === 'partner' && partnerType) {
      switch (partnerType) {
        case 'architect':
          return colors.architect;
        case 'hotel':
          return colors.hotel;
        default:
          return colors.partner;
      }
    }

    switch (category) {
      case 'partner':
        return colors.partner;
      case 'loyal':
        return colors.loyal;
      case 'regular':
        return colors.regular;
      default:
        return '';
    }
  };

  // Obtenir l'icône de la catégorie
  const getCategoryIcon = (category, partnerType = '', sellerGroup = '') => {
    if (sellerGroup === 'Ji-line GROUPE') {
      return <i className="fas fa-store" style={{ color: colors.jline, fontSize: '1.5rem' }}></i>;
    }

    if (category === 'partner' && partnerType) {
      switch (partnerType) {
        case 'architect':
          return <i className="fas fa-drafting-compass" style={{ color: colors.architect, fontSize: '1.5rem' }}></i>;
        case 'hotel':
          return <i className="fas fa-hotel" style={{ color: colors.hotel, fontSize: '1.5rem' }}></i>;
        default:
          return <i className="fas fa-handshake" style={{ color: colors.partner, fontSize: '1.5rem' }}></i>;
      }
    }

    switch (category) {
      case 'partner':
        return <i className="fas fa-handshake" style={{ color: colors.partner, fontSize: '1.5rem' }}></i>;
      case 'loyal':
        return <i className="fas fa-star" style={{ color: colors.loyal, fontSize: '1.5rem' }}></i>;
      default:
        return null;
    }
  };

  // Obtenir le libellé complet de la catégorie (avec type de partenaire si applicable)
  const getFullCategoryLabel = (client) => {
    if (client.sellerGroup === 'Ji-line GROUPE') {
      return 'Ji-line GROUPE';
    }

    let label = getCategoryLabel(client.category);

    if (client.category === 'partner' && client.partnerType) {
      switch (client.partnerType) {
        case 'architect':
          return `${label} (Architecte)`;
        case 'hotel':
          return `${label} (Hôtel)`;
        default:
          return label;
      }
    }

    return label;
  };

  // Gérer l'ouverture du modal pour ajouter un client
  const handleAddClient = () => {
    setModalAction('add');
    setFormData({
      name: '',
      email: '',
      phone: '',
      category: 'regular',
      partnerType: '',
      company: '',
      location: 'Tunis',
      sellerGroup: '',
      sellerBrand: ''
    });
    setShowModal(true);
  };

  // Gérer l'ouverture du modal pour modifier un client
  const handleEditClient = (client) => {
    setModalAction('edit');
    setCurrentClient(client);
    setFormData({
      name: client.name,
      email: client.email,
      phone: client.phone,
      category: client.category,
      partnerType: client.partnerType || '',
      company: client.company || '',
      location: client.location,
      sellerGroup: client.sellerGroup || '',
      sellerBrand: client.sellerBrand || ''
    });
    setShowModal(true);
  };

  // Gérer l'ouverture du modal pour supprimer un client
  const handleDeleteClient = (client) => {
    setModalAction('delete');
    setCurrentClient(client);
    setShowModal(true);
  };

  // Gérer la soumission du formulaire
  const handleFormSubmit = () => {
    if (modalAction === 'add') {
      const newClient = {
        id: clients.length > 0 ? Math.max(...clients.map((c) => c.id)) + 1 : 1,
        name: formData.name,
        email: formData.email,
        phone: formData.phone,
        category: formData.category,
        partnerType: formData.partnerType,
        company: formData.company,
        lastPurchase: new Date().toISOString().split('T')[0],
        totalPurchases: 0,
        location: formData.location,
        sellerGroup: formData.sellerGroup,
        sellerBrand: formData.sellerBrand
      };

      setClients([...clients, newClient]);
    } else if (modalAction === 'edit' && currentClient) {
      const updatedClients = clients.map((client) =>
        client.id === currentClient.id
          ? {
              ...client,
              name: formData.name,
              email: formData.email,
              phone: formData.phone,
              category: formData.category,
              partnerType: formData.partnerType,
              company: formData.company,
              location: formData.location,
              sellerGroup: formData.sellerGroup,
              sellerBrand: formData.sellerBrand
            }
          : client
      );

      setClients(updatedClients);
    } else if (modalAction === 'delete' && currentClient) {
      const updatedClients = clients.filter((client) => client.id !== currentClient.id);
      setClients(updatedClients);
    }

    setShowModal(false);
  };

  // Gérer le changement de page
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Effet pour réinitialiser la page lors des filtres
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchTerm]);

  // Gérer le changement des champs du formulaire
  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  return (
    <Container className="py-4" style={fontStyle}>
      {/* Titre principal */}
      <div className="text-center mb-5 position-relative">
        <h1
          className="mb-3"
          style={{
            fontSize: '2.2rem',
            fontWeight: 600,
            color: colors.primaryDark,
            letterSpacing: '1px'
          }}
        >
          GESTION DES CLIENTS
        </h1>
        <div
          className="mx-auto"
          style={{
            height: '3px',
            width: '120px',
            background: 'linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%)',
            borderRadius: '3px'
          }}
        />
      </div>

      {/* Barre de recherche et bouton d'ajout */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <Form.Group style={{ width: '300px' }}>
          <Form.Control
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              borderRadius: '20px',
              padding: '10px 20px'
            }}
          />
        </Form.Group>

        <div className="d-flex align-items-center">
          <Badge bg="light" text="dark" className="me-3">
            {filteredClients.length} client(s) trouvé(s)
          </Badge>

          <Button variant="primary" onClick={handleAddClient}>
            <i className="fas fa-plus me-2"></i> Ajouter un client
          </Button>
        </div>
      </div>

      {/* Onglets de catégories */}
      <Tabs
        activeKey={activeTab}
        onSelect={(k) => setActiveTab(k)}
        className="mb-4"
        style={{
          borderBottom: '2px solid #dee2e6'
        }}
      >
        <Tab eventKey="all" title={<span style={fontStyle}>Tous les clients</span>} />
        <Tab eventKey="partner" title={<span style={fontStyle}>Partenaires</span>} />
        <Tab eventKey="loyal" title={<span style={fontStyle}>Clients fidèles</span>} />
        <Tab eventKey="regular" title={<span style={fontStyle}>Clients simples</span>} />
        <Tab eventKey="jline" title={<span style={fontStyle}> GROUPES</span>} />
      </Tabs>

      {/* Liste des clients */}
      {currentClients.length > 0 ? (
        <>
          <Row className="g-4">
            {currentClients.map((client) => (
              <Col key={client.id} xs={12} md={6} lg={4}>
                <Card className="h-100 border-0 shadow-sm">
                  <Card.Body>
                    <div className="d-flex justify-content-between align-items-start mb-3">
                      <div>
                        <Card.Title
                          style={{
                            fontSize: '1.2rem',
                            fontWeight: 600,
                            color: colors.primaryDark,
                            marginBottom: '0.5rem'
                          }}
                        >
                          {client.name}
                        </Card.Title>
                        <Badge
                          style={{
                            backgroundColor: getCategoryColor(client.category, client.partnerType, client.sellerGroup),
                            fontSize: '0.75rem',
                            padding: '0.35em 0.65em'
                          }}
                        >
                          {getFullCategoryLabel(client)}
                        </Badge>
                        <Badge
                          className="ms-2"
                          style={{
                            backgroundColor: colors.location,
                            fontSize: '0.75rem',
                            padding: '0.35em 0.65em'
                          }}
                        >
                          {client.location}
                        </Badge>
                      </div>
                      {getCategoryIcon(client.category, client.partnerType, client.sellerGroup)}
                    </div>

                    <div className="mb-3">
                      <div className="d-flex align-items-center mb-2">
                        <i className="fas fa-envelope me-2" style={{ color: colors.accent }}></i>
                        <span>{client.email}</span>
                      </div>
                      <div className="d-flex align-items-center mb-2">
                        <i className="fas fa-phone me-2" style={{ color: colors.accent }}></i>
                        <span>{client.phone}</span>
                      </div>
                      {client.company && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-building me-2" style={{ color: colors.accent }}></i>
                          <span>{client.company}</span>
                        </div>
                      )}
                      {client.sellerGroup && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-store me-2" style={{ color: colors.accent }}></i>
                          <span>{client.sellerGroup}</span>
                        </div>
                      )}
                      {client.sellerBrand && (
                        <div className="d-flex align-items-center mb-2">
                          <i className="fas fa-tag me-2" style={{ color: colors.accent }}></i>
                          <span>{client.sellerBrand}</span>
                        </div>
                      )}
                    </div>

                    <div className="d-flex justify-content-between small text-muted">
                      <div>
                        <i className="fas fa-shopping-cart me-1"></i>
                        {client.totalPurchases} achat(s)
                      </div>
                      <div>Dernier achat: {new Date(client.lastPurchase).toLocaleDateString()}</div>
                    </div>
                  </Card.Body>
                  <Card.Footer className="bg-white border-0">
                    <div className="d-flex justify-content-end">
                      <Button variant="outline-primary" size="sm" className="me-2">
                        <i className="fas fa-eye me-1"></i> Voir
                      </Button>
                      <Button variant="outline-secondary" size="sm" className="me-2" onClick={() => handleEditClient(client)}>
                        <i className="fas fa-edit me-1"></i> Modifier
                      </Button>
                      <Button variant="outline-danger" size="sm" onClick={() => handleDeleteClient(client)}>
                        <i className="fas fa-trash-alt me-1"></i> Supprimer
                      </Button>
                    </div>
                  </Card.Footer>
                </Card>
              </Col>
            ))}
          </Row>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="d-flex justify-content-center mt-4">
              <Pagination>
                <Pagination.First onClick={() => handlePageChange(1)} disabled={currentPage === 1} />
                <Pagination.Prev onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1} />

                {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
                  <Pagination.Item key={pageNumber} active={pageNumber === currentPage} onClick={() => handlePageChange(pageNumber)}>
                    {pageNumber}
                  </Pagination.Item>
                ))}

                <Pagination.Next onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages} />
                <Pagination.Last onClick={() => handlePageChange(totalPages)} disabled={currentPage === totalPages} />
              </Pagination>
            </div>
          )}
        </>
      ) : (
        <Card className="text-center py-5 border-0 shadow-sm">
          <Card.Body>
            <i className="fas fa-users-slash mb-3" style={{ fontSize: '3rem', color: colors.primaryLight }}></i>
            <Card.Title style={{ color: colors.primaryLight }}>Aucun client trouvé</Card.Title>
            <Card.Text className="text-muted">
              {searchTerm ? "Essayez avec d'autres termes de recherche" : 'Ajoutez votre premier client'}
            </Card.Text>
            <Button variant="primary" className="mt-3" onClick={handleAddClient}>
              <i className="fas fa-plus me-2"></i> Ajouter un client
            </Button>
          </Card.Body>
        </Card>
      )}

      {/* Modal d'ajout/modification/suppression */}
      <Modal show={showModal} onHide={() => setShowModal(false)} centered>
        <Modal.Header closeButton>
          <Modal.Title>
            {modalAction === 'add' ? 'Ajouter un client' : modalAction === 'edit' ? 'Modifier le client' : 'Supprimer le client'}
          </Modal.Title>
        </Modal.Header>

        <Modal.Body>
          {modalAction === 'delete' ? (
            <p>
              Êtes-vous sûr de vouloir supprimer le client <strong>{currentClient?.name}</strong> ?
            </p>
          ) : (
            <Form>
              <Form.Group className="mb-3">
                <Form.Label>Nom complet</Form.Label>
                <Form.Control type="text" name="name" value={formData.name} onChange={handleFormChange} required />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Email</Form.Label>
                <Form.Control type="email" name="email" value={formData.email} onChange={handleFormChange} required />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Téléphone</Form.Label>
                <Form.Control type="text" name="phone" value={formData.phone} onChange={handleFormChange} required />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Entreprise</Form.Label>
                <Form.Control type="text" name="company" value={formData.company} onChange={handleFormChange} />
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Catégorie</Form.Label>
                <Form.Select name="category" value={formData.category} onChange={handleFormChange}>
                  <option value="regular">Client simple</option>
                  <option value="loyal">Client fidèle</option>
                  <option value="partner">Partenaire</option>
                </Form.Select>
              </Form.Group>

              {formData.category === 'partner' && (
                <Form.Group className="mb-3">
                  <Form.Label>Type de partenaire</Form.Label>
                  <Form.Select name="partnerType" value={formData.partnerType} onChange={handleFormChange}>
                    <option value="">Général</option>
                    <option value="architect">Architecte</option>
                    <option value="hotel">Hôtel</option>
                  </Form.Select>
                </Form.Group>
              )}

              <Form.Group className="mb-3">
                <Form.Label>Ville</Form.Label>
                <Form.Select name="location" value={formData.location} onChange={handleFormChange}>
                  <option value="Tunis">Tunis</option>
                  <option value="Sfax">Sfax</option>
                  <option value="Sousse">Sousse</option>
                  <option value="Monastir">Monastir</option>
                  <option value="Hammamet">Hammamet</option>
                </Form.Select>
              </Form.Group>

              <Form.Group className="mb-3">
                <Form.Label>Groupe</Form.Label>
                <Form.Select name="sellerGroup" value={formData.sellerGroup} onChange={handleFormChange}>
                  <option value="">Aucun</option>
                  <option value="Ji-line GROUPE"> GROUPE</option>
                </Form.Select>
              </Form.Group>

              {formData.sellerGroup === 'Ji-line GROUPE' && (
                <Form.Group className="mb-3">
                  <Form.Label>Marque</Form.Label>
                  <Form.Select name="sellerBrand" value={formData.sellerBrand} onChange={handleFormChange}>
                    <option value="">Sélectionner une marque</option>
                    <option value="Luxoria">Luxoria</option>
                    <option value="Modernio">Modernio</option>
                    <option value="Eleganto">Eleganto</option>
                  </Form.Select>
                </Form.Group>
              )}
            </Form>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Annuler
          </Button>
          <Button variant={modalAction === 'delete' ? 'danger' : 'primary'} onClick={handleFormSubmit}>
            {modalAction === 'add' ? 'Ajouter' : modalAction === 'edit' ? 'Enregistrer' : 'Supprimer'}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default ClientManagement;
