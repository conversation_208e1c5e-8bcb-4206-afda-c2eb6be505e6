// Keycloak configuration and utilities
// This file handles the integration with Keycloak for authentication

// Keycloak configuration - these should be environment variables in production
const KEYCLOAK_CONFIG = {
  url: process.env.REACT_APP_KEYCLOAK_URL || 'https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud',
  realm: process.env.REACT_APP_KEYCLOAK_REALM || 'jiheneline',
  clientId: process.env.REACT_APP_KEYCLOAK_CLIENT_ID || 'backoffice-client'
};

// Keycloak utility class
export class KeycloakService {
  constructor() {
    this.keycloak = null;
    this.initialized = false;
  }

  // Initialize Keycloak
  async init() {
    try {
      // For now, we'll simulate Keycloak initialization
      // In a real implementation, you would use the keycloak-js library
      console.log('Keycloak configuration:', KEYCLOAK_CONFIG);

      // Simulate initialization
      this.initialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Keycloak:', error);
      throw error;
    }
  }

  // Login with Keycloak
  async login() {
    try {
      if (!this.initialized) {
        await this.init();
      }

      // For development/demo purposes, we'll simulate a successful login
      // In a real implementation, this would redirect to Keycloak login page

      // Simulate getting tokens from Keycloak after successful authentication
      const mockTokens = this.generateMockTokens();

      return mockTokens;
    } catch (error) {
      console.error('Keycloak login failed:', error);
      throw error;
    }
  }

  // Logout from Keycloak
  async logout() {
    try {
      // In a real implementation, this would call keycloak.logout()
      console.log('Logging out from Keycloak...');
      return true;
    } catch (error) {
      console.error('Keycloak logout failed:', error);
      throw error;
    }
  }

  // Get current token
  getToken() {
    // In a real implementation, this would return keycloak.token
    return localStorage.getItem('access_token');
  }

  // Check if authenticated
  isAuthenticated() {
    // In a real implementation, this would return keycloak.authenticated
    return !!this.getToken();
  }

  // Generate mock tokens for development
  generateMockTokens() {
    // This is for development only - in production, tokens come from Keycloak
    const now = Math.floor(Date.now() / 1000);
    const mockPayload = {
      sub: 'mock-user-id-' + Math.random().toString(36).substr(2, 9),
      name: 'Admin User',
      email: '<EMAIL>',
      preferred_username: 'admin',
      given_name: 'Admin',
      family_name: 'User',
      exp: now + (60 * 60), // 1 hour from now
      iat: now,
      auth_time: now,
      jti: 'mock-jti-' + Math.random().toString(36).substr(2, 9),
      iss: `${KEYCLOAK_CONFIG.url}/realms/${KEYCLOAK_CONFIG.realm}`,
      aud: KEYCLOAK_CONFIG.clientId,
      typ: 'Bearer',
      azp: KEYCLOAK_CONFIG.clientId,
      session_state: 'mock-session-' + Math.random().toString(36).substr(2, 9),
      realm_access: {
        roles: ['admin', 'client']
      },
      resource_access: {
        [KEYCLOAK_CONFIG.clientId]: {
          roles: ['admin']
        }
      },
      scope: 'openid profile email',
      email_verified: true,
      roles: ['admin', 'client'] // Legacy field for compatibility
    };

    // Create a mock JWT token with proper header including kid
    const header = btoa(JSON.stringify({
      alg: 'RS256',
      typ: 'JWT',
      kid: 'mock-key-id-' + Math.random().toString(36).substr(2, 9)
    }));
    const payload = btoa(JSON.stringify(mockPayload));
    const signature = 'mock-signature-' + Math.random().toString(36).substr(2, 16);

    const mockAccessToken = `${header}.${payload}.${signature}`;

    return {
      access_token: mockAccessToken,
      refresh_token: 'mock-refresh-token',
      id_token: mockAccessToken,
      token_type: 'Bearer',
      expires_in: 3600
    };
  }

  // Parse JWT token
  parseJWT(token) {
    try {
      const base64Url = token.split('.')[1];
      const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split('')
          .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
          .join('')
      );
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to parse JWT token:', error);
      return null;
    }
  }

  // Get user info from token
  getUserInfo() {
    const token = this.getToken();
    if (!token) return null;

    const payload = this.parseJWT(token);
    if (!payload) return null;

    return {
      id: payload.sub,
      name: payload.name,
      email: payload.email,
      roles: payload.roles || []
    };
  }
}

// Create singleton instance
const keycloakService = new KeycloakService();

export default keycloakService;

// Helper function to redirect to Keycloak login
export const redirectToKeycloakLogin = () => {
  try {
    const loginUrl = `${KEYCLOAK_CONFIG.url}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/auth`;
    const params = new URLSearchParams({
      client_id: KEYCLOAK_CONFIG.clientId,
      redirect_uri: window.location.origin + '/auth/callback',
      response_type: 'code',
      scope: 'openid profile email'
    });

    const fullUrl = `${loginUrl}?${params.toString()}`;

    console.log('🔐 Redirecting to Keycloak...');
    console.log('🔐 Keycloak URL:', KEYCLOAK_CONFIG.url);
    console.log('🔐 Realm:', KEYCLOAK_CONFIG.realm);
    console.log('🔐 Client ID:', KEYCLOAK_CONFIG.clientId);
    console.log('🔐 Redirect URI:', window.location.origin + '/auth/callback');
    console.log('🔐 Full URL:', fullUrl);

    window.location.href = fullUrl;
  } catch (error) {
    console.error('🔐 Failed to redirect to Keycloak:', error);
    throw error;
  }
};

// Helper function to handle Keycloak callback
export const handleKeycloakCallback = async (code) => {
  try {
    console.log('🔐 Handling Keycloak callback with code:', code);

    // Exchange authorization code for tokens
    const tokenUrl = `${KEYCLOAK_CONFIG.url}/realms/${KEYCLOAK_CONFIG.realm}/protocol/openid-connect/token`;

    const params = new URLSearchParams({
      grant_type: 'authorization_code',
      client_id: KEYCLOAK_CONFIG.clientId,
      code: code,
      redirect_uri: window.location.origin + '/auth/callback'
    });

    console.log('🔐 Exchanging code for tokens...');

    try {
      console.log('🔐 Token exchange request details:', {
        url: tokenUrl,
        client_id: KEYCLOAK_CONFIG.clientId,
        redirect_uri: window.location.origin + '/auth/callback',
        code_length: code.length
      });

      const response = await fetch(tokenUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: params.toString()
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🔐 Token exchange error:', response.status, errorText);

        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: 'unknown', error_description: errorText };
        }

        // Handle specific OAuth2 errors
        if (errorData.error === 'invalid_grant') {
          console.error('🔐 Authorization code is invalid or expired');
          console.error('🔐 This usually happens when:');
          console.error('🔐 1. Code was already used');
          console.error('🔐 2. Code expired (usually 60 seconds)');
          console.error('🔐 3. Redirect URI mismatch');
          console.error('🔐 4. Client ID mismatch');
        }

        throw new Error(`Token exchange failed: ${response.status} - ${errorData.error_description || errorData.error}`);
      }

      const tokens = await response.json();

      console.log('🔐 Token exchange successful');
      console.log('🔐 Received real tokens from Keycloak');

      return {
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
        id_token: tokens.id_token
      };
    } catch (error) {
      console.error('🔐 Token exchange failed, using fallback:', error);

      // For invalid_grant errors, we should not use fallback as it indicates a real auth issue
      if (error.message.includes('invalid_grant')) {
        console.error('🔐 Cannot use fallback for invalid_grant error - this indicates an authentication problem');
        throw new Error('Authentication failed: Authorization code is invalid. Please try logging in again.');
      }

      // Fallback: Generate mock tokens for development (only for network/server errors)
      console.log('🔐 Generating fallback tokens...');
      const tokens = keycloakService.generateMockTokens();
      return tokens;
    }
  } catch (error) {
    console.error('🔐 Failed to handle Keycloak callback:', error);
    throw error;
  }
};
