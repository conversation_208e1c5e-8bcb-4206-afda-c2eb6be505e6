import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Al<PERSON>, Spin<PERSON>, Badge, <PERSON>, Col } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';
import { FaEdit, FaTrash, FaPlus, FaCheck } from 'react-icons/fa';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';

const Listpromotions = () => {
  const API_URL = 'https://laravel-api.fly.dev/api';

  // États
  const [promotions, setPromotions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [currentPromotion, setCurrentPromotion] = useState(null);
  const [modalAction, setModalAction] = useState('create'); // 'create' or 'edit'
  const [formData, setFormData] = useState({
    nom: '',
    code: '',
    description: '',
    type: 'pourcentage',
    valeur: '',
    statut: 'active',
    date_debut: null,
    date_fin: null,
    priorite: 10,
    cumulable: false,
    produits: [],
    collections: [],
    profils_remise: []
  });

  // Types et options
  const typesPromotion = [
    { value: 'pourcentage', label: 'Pourcentage' },
    { value: 'montant_fixe', label: 'Montant fixe' },
    { value: 'gratuit', label: 'Gratuit' }
  ];

  const statutsPromotion = [
    { value: 'active', label: 'Active' },
    { value: 'inactive', label: 'Inactive' },
    { value: 'programmee', label: 'Programmée' }
  ];

  // Charger les promotions
  useEffect(() => {
    const fetchPromotions = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/promotions`);

        if (!response.ok) {
          throw new Error('Erreur de chargement des promotions');
        }

        const data = await response.json();
        // Ensure promotions is always an array
        const promotionsData = data.data || data;
        setPromotions(Array.isArray(promotionsData) ? promotionsData : []);
      } catch (err) {
        console.error('Erreur:', err);
        setError(err.message || 'Erreur lors du chargement des promotions');
      } finally {
        setLoading(false);
      }
    };

    fetchPromotions();
  }, []);

  // Gestion des changements du formulaire
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Gestion des dates
  const handleDateChange = (date, field) => {
    setFormData({
      ...formData,
      [field]: date
    });
  };

  // Ouvrir le modal pour créer une nouvelle promotion
  const handleCreate = () => {
    setModalAction('create');
    setCurrentPromotion(null);
    setFormData({
      nom: '',
      code: '',
      description: '',
      type: 'pourcentage',
      valeur: '',
      statut: 'active',
      date_debut: null,
      date_fin: null,
      priorite: 10,
      cumulable: false,
      produits: [],
      collections: [],
      profils_remise: []
    });
    setShowModal(true);
  };

  // Ouvrir le modal pour modifier une promotion
  const handleEdit = (promotion) => {
    setModalAction('edit');
    setCurrentPromotion(promotion);
    setFormData({
      nom: promotion.nom,
      code: promotion.code,
      description: promotion.description,
      type: promotion.type,
      valeur: promotion.valeur,
      statut: promotion.statut,
      date_debut: promotion.date_debut ? new Date(promotion.date_debut) : null,
      date_fin: promotion.date_fin ? new Date(promotion.date_fin) : null,
      priorite: promotion.priorite || 10,
      cumulable: promotion.cumulable || false,
      produits: promotion.produits || [],
      collections: promotion.collections || [],
      profils_remise: promotion.profils_remise || []
    });
    setShowModal(true);
  };

  // Supprimer une promotion
  const handleDelete = async (id) => {
    if (!window.confirm('Êtes-vous sûr de vouloir supprimer cette promotion ?')) {
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/promotions/${id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression');
      }

      setPromotions(promotions.filter((p) => p.id !== id));
      setSuccess('Promotion supprimée avec succès');
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || 'Erreur lors de la suppression');
    } finally {
      setLoading(false);
    }
  };

  // Soumettre le formulaire (création ou modification)
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setLoading(true);
      setError(null);

      const payload = {
        ...formData,
        date_debut: formData.date_debut ? formData.date_debut.toISOString() : null,
        date_fin: formData.date_fin ? formData.date_fin.toISOString() : null
      };

      let response;
      if (modalAction === 'create') {
        response = await fetch(`${API_URL}/promotions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        });
      } else {
        response = await fetch(`${API_URL}/promotions/${currentPromotion.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(payload)
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Erreur lors de l'opération");
      }

      const data = await response.json();
      setSuccess(`Promotion ${modalAction === 'create' ? 'créée' : 'modifiée'} avec succès!`);

      // Mettre à jour la liste des promotions
      const updatedResponse = await fetch(`${API_URL}/promotions`);
      const updatedData = await updatedResponse.json();
      // Ensure promotions is always an array
      const promotionsData = updatedData.data || updatedData;
      setPromotions(Array.isArray(promotionsData) ? promotionsData : []);

      setShowModal(false);
    } catch (err) {
      console.error('Erreur:', err);
      setError(err.message || "Erreur lors de l'opération");
    } finally {
      setLoading(false);
    }
  };

  // Formater la date pour l'affichage
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  return (
    <Container className="py-4">
      <h2 className="mb-4">Gestion des Promotions</h2>
      {/* Messages d'alerte */}
      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          <i className="fas fa-exclamation-circle me-2"></i>
          {error}
        </Alert>
      )}
      {success && (
        <Alert variant="success" onClose={() => setSuccess(null)} dismissible>
          <i className="fas fa-check-circle me-2"></i>
          {success}
        </Alert>
      )}
      {/* Bouton pour ajouter une promotion */}
      <div className="d-flex justify-content-end mb-4">
        <Button variant="primary" onClick={handleCreate}>
          <FaPlus className="me-2" />
          Ajouter une promotion
        </Button>
      </div>{' '}
      {/* Tableau des promotions */}
      {loading ? (
        <div className="text-center my-5">
          <Spinner animation="border" variant="primary" />
          <p>Chargement des promotions...</p>
        </div>
      ) : promotions.length === 0 ? (
        <Alert variant="info">Aucune promotion disponible</Alert>
      ) : (
        <Table striped bordered hover responsive>
          <thead>
            <tr>
              <th>Nom</th>
              <th>Code</th>
              <th>Type</th>
              <th>Valeur</th>
              <th>Statut</th>
              <th>Dates</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {Array.isArray(promotions) &&
              promotions.map((promotion) => (
                <tr key={promotion.id}>
                  <td>{promotion.nom}</td>
                  <td>{promotion.code || '-'}</td>
                  <td>
                    {promotion.type === 'pourcentage' ? 'Pourcentage' : promotion.type === 'montant_fixe' ? 'Montant fixe' : 'Gratuit'}
                  </td>
                  <td>
                    {promotion.valeur}
                    {promotion.type === 'pourcentage' ? '%' : promotion.type === 'montant_fixe' ? '€' : ''}
                  </td>
                  <td>
                    <Badge bg={promotion.statut === 'active' ? 'success' : promotion.statut === 'inactive' ? 'danger' : 'warning'}>
                      {promotion.statut === 'active' ? 'Active' : promotion.statut === 'inactive' ? 'Inactive' : 'Programmée'}
                    </Badge>
                  </td>
                  <td>
                    {formatDate(promotion.date_debut)} - {formatDate(promotion.date_fin)}
                  </td>
                  <td>
                    <Button variant="outline-primary" size="sm" className="me-2" onClick={() => handleEdit(promotion)}>
                      <FaEdit />
                    </Button>
                    <Button variant="outline-danger" size="sm" onClick={() => handleDelete(promotion.id)}>
                      <FaTrash />
                    </Button>
                  </td>
                </tr>
              ))}
          </tbody>
        </Table>
      )}
      {/* Modal pour créer/modifier une promotion */}
      <Modal show={showModal} onHide={() => setShowModal(false)} size="lg">
        <Modal.Header closeButton>
          <Modal.Title>{modalAction === 'create' ? 'Ajouter une promotion' : 'Modifier la promotion'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form onSubmit={handleSubmit}>
            <Row>
              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Nom de la promotion <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control type="text" name="nom" value={formData.nom} onChange={handleChange} required />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Code promotionnel</Form.Label>
                  <Form.Control type="text" name="code" value={formData.code} onChange={handleChange} />
                </Form.Group>
              </Col>

              <Col md={12}>
                <Form.Group className="mb-3">
                  <Form.Label>Description</Form.Label>
                  <Form.Control as="textarea" name="description" value={formData.description} onChange={handleChange} rows={3} />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Type de promotion <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Select name="type" value={formData.type} onChange={handleChange} required>
                    {Array.isArray(typesPromotion) &&
                      typesPromotion.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                  </Form.Select>
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Valeur <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Control
                    type="number"
                    name="valeur"
                    value={formData.valeur}
                    onChange={handleChange}
                    min="0"
                    step={formData.type === 'pourcentage' ? '0.1' : '0.01'}
                    required
                  />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>
                    Statut <span className="text-danger">*</span>
                  </Form.Label>
                  <Form.Select name="statut" value={formData.statut} onChange={handleChange} required>
                    {Array.isArray(statutsPromotion) &&
                      statutsPromotion.map((statut) => (
                        <option key={statut.value} value={statut.value}>
                          {statut.label}
                        </option>
                      ))}
                  </Form.Select>
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de début</Form.Label>
                  <DatePicker
                    selected={formData.date_debut}
                    onChange={(date) => handleDateChange(date, 'date_debut')}
                    selectsStart
                    startDate={formData.date_debut}
                    endDate={formData.date_fin}
                    className="form-control"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                  />
                </Form.Group>
              </Col>

              <Col md={6}>
                <Form.Group className="mb-3">
                  <Form.Label>Date de fin</Form.Label>
                  <DatePicker
                    selected={formData.date_fin}
                    onChange={(date) => handleDateChange(date, 'date_fin')}
                    selectsEnd
                    startDate={formData.date_debut}
                    endDate={formData.date_fin}
                    minDate={formData.date_debut}
                    className="form-control"
                    dateFormat="dd/MM/yyyy"
                    isClearable
                  />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Label>Priorité</Form.Label>
                  <Form.Control type="number" name="priorite" value={formData.priorite} onChange={handleChange} min="1" />
                </Form.Group>
              </Col>

              <Col md={4}>
                <Form.Group className="mb-3">
                  <Form.Check
                    type="checkbox"
                    name="cumulable"
                    label="Promotion cumulable"
                    checked={formData.cumulable}
                    onChange={handleChange}
                  />
                </Form.Group>
              </Col>
            </Row>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowModal(false)}>
            Annuler
          </Button>
          <Button variant="primary" onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <Spinner as="span" size="sm" animation="border" className="me-2" />
                Enregistrement...
              </>
            ) : (
              <>
                <FaCheck className="me-2" />
                {modalAction === 'create' ? 'Créer' : 'Modifier'}
              </>
            )}
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
  );
};

export default Listpromotions;
