const API_URL = 'https://laravel-api.fly.dev/api';

// Partners
export async function fetchPartners(params = {}) {
  const queryParams = new URLSearchParams();

  if (params.search) queryParams.append('search', params.search);
  if (params.page) queryParams.append('page', params.page);
  if (params.per_page) queryParams.append('per_page', params.per_page);

  const url = `${API_URL}/partners${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;

  const res = await fetch(url);
  if (!res.ok) throw new Error('Error loading partners');
  return res.json();
}

export async function fetchPartnerById(id) {
  const res = await fetch(`${API_URL}/partners/${id}`);
  if (!res.ok) throw new Error('Error loading partner');
  return res.json();
}

export async function createPartner(data) {
  const res = await fetch(`${API_URL}/partners`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating partner');
  return res.json();
}

export async function updatePartner(id, data) {
  const res = await fetch(`${API_URL}/partners/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating partner');
  return res.json();
}

export async function deletePartner(id) {
  const res = await fetch(`${API_URL}/partners/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting partner');
  return res.json();
}

// Partner Discount Profiles
export async function fetchPartnerDiscountProfiles() {
  const res = await fetch(`${API_URL}/partner-discount-profiles`);
  if (!res.ok) throw new Error('Error loading partner discount profiles');
  return res.json();
}

export async function fetchPartnerDiscountProfileById(id) {
  const res = await fetch(`${API_URL}/partner-discount-profiles/${id}`);
  if (!res.ok) throw new Error('Error loading partner discount profile');
  return res.json();
}

export async function createPartnerDiscountProfile(data) {
  const res = await fetch(`${API_URL}/partner-discount-profiles`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating partner discount profile');
  return res.json();
}

export async function updatePartnerDiscountProfile(id, data) {
  const res = await fetch(`${API_URL}/partner-discount-profiles/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating partner discount profile');
  return res.json();
}

export async function deletePartnerDiscountProfile(id) {
  const res = await fetch(`${API_URL}/partner-discount-profiles/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting partner discount profile');
  return res.json();
}

// Assign discount profile to partner
export async function assignDiscountProfileToPartner(partnerId, profileId) {
  const res = await fetch(`${API_URL}/partners/${partnerId}/discount-profile`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ profile_id: profileId })
  });
  if (!res.ok) throw new Error('Error assigning discount profile to partner');
  return res.json();
}

// Remove discount profile from partner
export async function removeDiscountProfileFromPartner(partnerId) {
  const res = await fetch(`${API_URL}/partners/${partnerId}/discount-profile`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error removing discount profile from partner');
  return res.json();
}

// Partner Points of Sale
export async function fetchPartnerPointsOfSale(partnerId) {
  const res = await fetch(`${API_URL}/partners/${partnerId}/points-of-sale`);
  if (!res.ok) throw new Error('Error loading partner points of sale');
  return res.json();
}

export async function fetchAllPointsOfSale() {
  const res = await fetch(`${API_URL}/points-of-sale`);
  if (!res.ok) throw new Error('Error loading points of sale');
  return res.json();
}

export async function fetchPointOfSaleById(id) {
  const res = await fetch(`${API_URL}/points-of-sale/${id}`);
  if (!res.ok) throw new Error('Error loading point of sale');
  return res.json();
}

export async function createPointOfSale(data) {
  const res = await fetch(`${API_URL}/points-of-sale`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error creating point of sale');
  return res.json();
}

export async function updatePointOfSale(id, data) {
  const res = await fetch(`${API_URL}/points-of-sale/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  });
  if (!res.ok) throw new Error('Error updating point of sale');
  return res.json();
}

export async function deletePointOfSale(id) {
  const res = await fetch(`${API_URL}/points-of-sale/${id}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error deleting point of sale');
  return res.json();
}

export async function assignPointOfSaleToPartner(partnerId, pointOfSaleId) {
  const res = await fetch(`${API_URL}/partners/${partnerId}/points-of-sale`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ point_of_sale_id: pointOfSaleId })
  });
  if (!res.ok) throw new Error('Error assigning point of sale to partner');
  return res.json();
}

export async function removePointOfSaleFromPartner(partnerId, pointOfSaleId) {
  const res = await fetch(`${API_URL}/partners/${partnerId}/points-of-sale/${pointOfSaleId}`, {
    method: 'DELETE'
  });
  if (!res.ok) throw new Error('Error removing point of sale from partner');
  return res.json();
}
