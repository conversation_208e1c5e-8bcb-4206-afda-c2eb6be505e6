# Authentication Implementation

## Overview

This document describes the authentication system implementation based on the Keycloak integration requirements. The system provides secure authentication using JWT tokens and role-based access control.

## Architecture

### Components

1. **AuthService** (`src/services/authService.js`)
   - Handles API communication with the Laravel backend
   - Manages token storage and validation
   - Provides authentication state management

2. **AuthContext** (`src/contexts/AuthContext.jsx`)
   - React context for global authentication state
   - Provides authentication hooks and methods
   - Manages user session and roles

3. **ProtectedRoute** (`src/components/ProtectedRoute.jsx`)
   - Route protection component
   - Role-based access control
   - Automatic redirects for unauthorized access

4. **Keycloak Integration** (`src/utils/keycloak.js`)
   - Keycloak client configuration
   - Token exchange utilities
   - Authentication flow management

## Authentication Flow

### 1. Login Process

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Keycloak
    participant Backend

    User->>Frontend: Click Login
    Frontend->>Keycloak: Redirect to Keycloak
    Keycloak->>User: Show Login Form
    User->>Keycloak: Enter Credentials
    Keycloak->>Frontend: Redirect with Auth Code
    Frontend->>Backend: POST /api/auth/verify
    Backend->>Keycloak: Verify Token
    Keycloak->>Backend: Token Valid + User Info
    Backend->>Frontend: User Data + Session
    Frontend->>User: Redirect to Dashboard
```

### 2. Registration Process

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant Admin
    participant Keycloak

    User->>Frontend: Fill Registration Form
    Frontend->>Frontend: Validate Form Data
    Frontend->>Admin: Send Registration Request
    Admin->>Admin: Review Request
    Admin->>Keycloak: Create User Account
    Admin->>User: Send Account Activation
    User->>Keycloak: Activate Account
    User->>Frontend: Login with New Account
```

### 3. Protected Route Access

```mermaid
sequenceDiagram
    participant User
    participant ProtectedRoute
    participant AuthContext
    participant Backend

    User->>ProtectedRoute: Access Protected Page
    ProtectedRoute->>AuthContext: Check Authentication
    AuthContext->>AuthContext: Check Token Validity
    alt Token Expired
        AuthContext->>Backend: POST /api/auth/refresh
        Backend->>AuthContext: New Token
    end
    AuthContext->>ProtectedRoute: Authentication Status
    alt Authenticated
        ProtectedRoute->>User: Show Protected Content
    else Not Authenticated
        ProtectedRoute->>User: Redirect to Login
    end
```

## API Integration

### Authentication Endpoints

#### Verify Token
```http
POST /api/auth/verify
Content-Type: application/json

{
  "access_token": "string",
  "refresh_token": "string",
  "id_token": "string"
}
```

#### Logout
```http
POST /api/auth/logout
Cookie: access_token=<token>
```

#### Refresh Token
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refresh_token": "string"
}
```

#### Get Current User
```http
GET /api/auth/user
Authorization: Bearer <access_token>
```

## Usage

### 1. Wrap App with AuthProvider

```jsx
import { AuthProvider } from 'contexts/AuthContext';

function App() {
  return (
    <AuthProvider>
      <YourAppComponents />
    </AuthProvider>
  );
}
```

### 2. Use Authentication Hook

```jsx
import { useAuth } from 'contexts/AuthContext';

function MyComponent() {
  const { user, isAuthenticated, login, logout, hasRole } = useAuth();

  if (!isAuthenticated) {
    return <div>Please log in</div>;
  }

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      {hasRole('admin') && <AdminPanel />}
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

### 3. Protect Routes

```jsx
import ProtectedRoute from 'components/ProtectedRoute';

// Require authentication
<ProtectedRoute>
  <DashboardComponent />
</ProtectedRoute>

// Require specific roles
<ProtectedRoute requiredRoles={['admin']}>
  <AdminComponent />
</ProtectedRoute>
```

## Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
REACT_APP_KEYCLOAK_URL=https://keycloak-prod.1squalq6nmfj.eu-de.codeengine.appdomain.cloud
REACT_APP_KEYCLOAK_REALM=jihene-line
REACT_APP_KEYCLOAK_CLIENT_ID=backoffice-client
REACT_APP_API_URL=https://laravel-api.fly.dev/api
```

### Keycloak Setup

1. **Create Realm**: `jihene-line`
2. **Create Client**: `backoffice-client`
3. **Configure Client**:
   - Client Protocol: `openid-connect`
   - Access Type: `public`
   - Valid Redirect URIs: `http://localhost:3000/auth/callback`, `https://your-domain.com/auth/callback`
   - Web Origins: `http://localhost:3000`, `https://your-domain.com`

## Security Features

1. **JWT Token Validation**: All tokens are verified with the backend
2. **Automatic Token Refresh**: Expired tokens are automatically refreshed
3. **Role-Based Access Control**: Routes can require specific roles
4. **Secure Storage**: Tokens are stored in localStorage (consider httpOnly cookies for production)
5. **CSRF Protection**: API calls include credentials for session management

## Development vs Production

### Development Mode
- Mock tokens are generated for testing
- Simplified authentication flow
- Console logging for debugging

### Production Mode
- Real Keycloak integration
- Secure token handling
- Error logging and monitoring

## Troubleshooting

### Common Issues

1. **Token Expired**: Automatically handled by refresh mechanism
2. **Invalid Credentials**: User redirected to login page
3. **Network Errors**: Graceful error handling with user feedback
4. **Role Permissions**: Clear error messages for unauthorized access

### Debug Mode

Enable debug logging by setting:
```javascript
localStorage.setItem('auth_debug', 'true');
```

## Testing

### Unit Tests
- AuthService methods
- AuthContext state management
- ProtectedRoute behavior

### Integration Tests
- Login flow
- Token refresh
- Role-based access

### E2E Tests
- Complete authentication workflow
- Protected route navigation
- Logout functionality
