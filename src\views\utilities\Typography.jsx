import React, { useState, useEffect } from 'react';
import EditProduit from '../Forms/EditProduit';
import axios from 'axios';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Spinner, Card, Container, Row, Col, Button, Pagination, Form, Modal, Badge } from 'react-bootstrap';
import 'bootstrap/dist/css/bootstrap.min.css';

// Configuration Axios
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

const fontStyle = {
  fontFamily: "'Montserrat', sans-serif",
  fontWeight: 500
};

const colors = {
  primaryDark: '#2a3f5f',
  primaryLight: '#3a537b',
  accent: '#3a8dde',
  lightGray: '#f8f9fa'
};

const API_BASE_URL = 'https://laravel-api.fly.dev/api';
const PRODUCTS_PER_PAGE = 8;

const BrandCatalog = () => {
  // États
  const [brands, setBrands] = useState([]);
  const [products, setProducts] = useState([]);
  const [filteredProducts, setFilteredProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [loading, setLoading] = useState({
    brands: false,
    products: false,
    productDetails: false,
    saving: false
  });
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('brands');
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [productToDelete, setProductToDelete] = useState(null);

  // Show delete confirmation modal
  const handleShowDeleteModal = (product) => {
    setProductToDelete(product);
    setShowDeleteModal(true);
  };

  // Hide delete confirmation modal
  const handleCloseDeleteModal = () => {
    setProductToDelete(null);
    setShowDeleteModal(false);
  };

  // Delete product
  const handleDeleteProduct = async () => {
    if (!productToDelete?.id) return;
    setLoading((prev) => ({ ...prev, products: true }));
    try {
      await axios.delete(`${API_BASE_URL}/produits/${productToDelete.id}`);
      const updatedProducts = products.filter((p) => p.id !== productToDelete.id);
      setProducts(updatedProducts);
      setFilteredProducts(updatedProducts);
      handleCloseDeleteModal();
    } catch (err) {
      handleApiError(err, 'Erreur lors de la suppression du produit');
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [showEditProduit, setShowEditProduit] = useState(false);
  const [editProduitData, setEditProduitData] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [showProductModal, setShowProductModal] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);

  // Form state
  const [productForm, setProductForm] = useState({
    nom_produit: '',
    reference_produit: '',
    prix_produit: '',
    quantite_produit: 0,
    image_produit: '',
    description_produit: ''
  });

  // Pagination
  const indexOfLastProduct = currentPage * PRODUCTS_PER_PAGE;
  const indexOfFirstProduct = indexOfLastProduct - PRODUCTS_PER_PAGE;
  const currentProducts = filteredProducts.slice(indexOfFirstProduct, indexOfLastProduct);
  const totalPages = Math.ceil(filteredProducts.length / PRODUCTS_PER_PAGE);

  // Effects
  useEffect(() => {
    // Load Google Font
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    fetchBrands();
  }, []);

  useEffect(() => {
    if (products.length > 0) {
      handleSearch();
    }
  }, [searchTerm, products, selectedCategory, selectedSubCategory]);

  // API Functions
  const fetchBrands = async () => {
    try {
      setLoading((prev) => ({ ...prev, brands: true }));
      setError(null);
      const response = await axios.get(`${API_BASE_URL}/marques`);
      setBrands(response.data || []);
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des marques');
    } finally {
      setLoading((prev) => ({ ...prev, brands: false }));
    }
  };

  const fetchProducts = async (brand) => {
    if (!brand?.id) return;

    try {
      setSelectedBrand(brand);
      setCurrentPage(1);
      setSearchTerm('');
      setSelectedCategory(null);
      setSelectedSubCategory(null);
      setLoading((prev) => ({ ...prev, products: true }));
      setError(null);

      // Fetch products (without images)
      const response = await axios.get(`${API_BASE_URL}/marques/${brand.id}/produits`, {
        params: {
          with: 'sousSousCategorie,sousSousCategorie.sousCategorie,sousSousCategorie.sousCategorie.categorie'
        }
      });
      const productsData = response.data || [];

      // Fetch images for each product
      const productsWithImages = await Promise.all(
        productsData.map(async (product) => {
          try {
            const imgRes = await axios.get(`${API_BASE_URL}/images/get`, {
              params: { model_type: 'produit', model_id: product.id }
            });
            return { ...product, images: imgRes.data.images || [] };
          } catch {
            return { ...product, images: [] };
          }
        })
      );

      setProducts(productsWithImages);
      setFilteredProducts(productsWithImages);
      setActiveTab('products');
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des produits');
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };

  const fetchProductDetails = async (productId) => {
    if (!productId) return;

    try {
      setLoading((prev) => ({ ...prev, productDetails: true }));
      setError(null);

      // Fetch product details (without images)
      const response = await axios.get(`${API_BASE_URL}/produits/${productId}`, {
        params: {
          with: 'sousSousCategorie,sousSousCategorie.sousCategorie,sousSousCategorie.sousCategorie.categorie'
        }
      });
      const productData = response.data || {};

      // Fetch images for this product
      let images = [];
      try {
        const imgRes = await axios.get(`${API_BASE_URL}/images/get`, {
          params: { model_type: 'produit', model_id: productId }
        });
        images = imgRes.data.images || [];
      } catch {}

      const productWithImages = { ...productData, images };
      setSelectedProduct(productWithImages);
      setProductForm({
        nom_produit: productWithImages.nom_produit || '',
        reference_produit: productWithImages.reference_produit || '',
        prix_produit: productWithImages.prix_produit || '',
        quantite_produit: productWithImages.quantite_produit || 0,
        description_produit: productWithImages.description_produit || ''
      });
      setShowProductModal(true);
    } catch (err) {
      handleApiError(err, 'Erreur de chargement des détails');
    } finally {
      setLoading((prev) => ({ ...prev, productDetails: false }));
    }
  };

  const handleApiError = (error, defaultMessage) => {
    console.error(error);
    setError(error.response?.data?.message || error.message || defaultMessage);
  };

  // Filter and Search
  const handleSearch = () => {
    const term = searchTerm.toLowerCase().trim();

    let results = [...products];

    // Filter by search term
    if (term) {
      results = results.filter(
        (product) => product.nom_produit?.toLowerCase().includes(term) || product.reference_produit?.toLowerCase().includes(term)
      );
    }

    // Filter by category
    if (selectedCategory) {
      results = results.filter((product) => product.sousSousCategorie?.sousCategorie?.categorie_id === selectedCategory.id);
    }

    // Filter by subcategory
    if (selectedSubCategory) {
      results = results.filter((product) => product.sousSousCategorie?.sous_categorie_id === selectedSubCategory.id);
    }

    setFilteredProducts(results);
    setCurrentPage(1);
  };

  // Product Handlers
  const handleProductClick = (product) => {
    if (product?.id) {
      fetchProductDetails(product.id);
    }
  };

  const handleEditClick = () => {
    setEditMode(true);
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setProductForm((prev) => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveChanges = async () => {
    if (!selectedProduct?.id) return;

    try {
      setLoading((prev) => ({ ...prev, saving: true }));
      setError(null);

      await axios.put(`${API_BASE_URL}/produits/${selectedProduct.id}`, productForm);

      // Optimistic update
      const updatedProducts = products.map((p) => (p.id === selectedProduct.id ? { ...p, ...productForm } : p));

      setProducts(updatedProducts);
      setFilteredProducts(updatedProducts);
      setSelectedProduct((prev) => ({ ...prev, ...productForm }));
      setEditMode(false);
    } catch (err) {
      handleApiError(err, 'Erreur lors de la mise à jour');
    } finally {
      setLoading((prev) => ({ ...prev, saving: false }));
    }
  };

  // UI Components
  const renderPagination = () =>
    totalPages > 1 && (
      <div className="d-flex justify-content-center my-4">
        <Pagination className="mb-0">
          <Pagination.Prev onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))} disabled={currentPage === 1} />
          {Array.from({ length: totalPages }, (_, i) => (
            <Pagination.Item
              key={i + 1}
              active={i + 1 === currentPage}
              onClick={() => setCurrentPage(i + 1)}
              style={{
                margin: '0 4px',
                minWidth: '40px',
                textAlign: 'center',
                fontWeight: i + 1 === currentPage ? 600 : 500,
                border: i + 1 === currentPage ? 'none' : '1px solid #dee2e6'
              }}
            >
              {i + 1}
            </Pagination.Item>
          ))}
          <Pagination.Next onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))} disabled={currentPage === totalPages} />
        </Pagination>
      </div>
    );

  const handleEditProduit = (product) => {
    setEditProduitData(product);
    setShowEditProduit(true);
  };

  const handleEditProduitSuccess = () => {
    setShowEditProduit(false);
    setEditProduitData(null);
    // Refresh products for the selected brand
    if (selectedBrand) fetchProducts(selectedBrand);
  };

  // Utility function to get the primary image URL
  const getPrimaryImageUrl = (product) => {
    if (product.images && product.images.length > 0) {
      const primary = product.images.find((img) => img.is_primary) || product.images[0];
      // Prefer direct_url, then url, then thumbnail_large, then thumbnail_medium, then path
      return (
        primary.direct_url ||
        primary.url ||
        primary.thumbnail_large ||
        primary.thumbnail_medium ||
        (primary.path ? `${API_BASE_URL}/images/file/${primary.path}` : null) ||
        'https://via.placeholder.com/300'
      );
    }
    return 'https://via.placeholder.com/300';
  };

  const renderProductCard = (product) => (
    <Card className="h-100 border-0 shadow-sm product-card position-relative">
      <Button
        variant="danger"
        size="sm"
        style={{
          position: 'absolute',
          top: 10,
          right: 10,
          zIndex: 2,
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem',
          padding: '0.25rem 0.5rem'
        }}
        onClick={(e) => {
          e.stopPropagation();
          handleShowDeleteModal(product);
        }}
        title="Supprimer le produit"
      >
        <i className="fas fa-trash"></i>
        <span style={{ fontSize: '0.85em', fontWeight: 500 }}>Supprimer</span>
      </Button>
      <div className="product-image-container" onClick={() => handleProductClick(product)}>
        <Card.Img variant="top" src={getPrimaryImageUrl(product)} alt={product.nom_produit} className="product-image" />
      </div>
      <Card.Body className="d-flex flex-column">
        <Button
          variant="outline-primary"
          size="sm"
          className="mb-2 align-self-end"
          onClick={(e) => {
            e.stopPropagation();
            handleEditProduit(product);
          }}
        >
          Éditer
        </Button>
        <Card.Title className="product-title">{product.nom_produit}</Card.Title>
        <Card.Text className="product-reference">Réf: {product.reference_produit || 'N/A'}</Card.Text>

        {product.sousSousCategorie?.sousCategorie?.categorie && (
          <div className="product-categories mb-2">
            <Badge bg="light" text="dark" className="me-1">
              {product.sousSousCategorie.sousCategorie.categorie.nom}
            </Badge>
            <Badge bg="light" text="dark">
              {product.sousSousCategorie.sousCategorie.nom}
            </Badge>
          </div>
        )}

        <div className="product-footer mt-auto">
          <span className="product-price">{product.prix_produit ? `${product.prix_produit} €` : 'Prix sur demande'}</span>
          <span className={`product-stock ${product.quantite_produit > 0 ? 'in-stock' : 'out-of-stock'}`}>
            {product.quantite_produit > 0 ? 'En stock' : 'Rupture'}
          </span>
        </div>
      </Card.Body>
    </Card>
  );

  return (
    <Container className="py-4 catalog-container" style={fontStyle}>
      {/* Header */}
      <div className="text-center mb-5 catalog-header">
        <h1 className="catalog-title">CATALOGUE DES MARQUES</h1>
        <div className="catalog-divider" />
      </div>

      {error && (
        <Alert variant="danger" onClose={() => setError(null)} dismissible>
          {error}
        </Alert>
      )}

      <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-4 catalog-tabs">
        {/* Brands Tab */}
        <Tab eventKey="brands" title="Marques">
          {loading.brands ? (
            <div className="text-center py-5">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : brands.length > 0 ? (
            <Row xs={1} md={2} lg={3} xl={4} className="g-4">
              {brands.map((brand) => (
                <Col key={brand.id}>
                  <Card className="h-100 brand-card" onClick={() => fetchProducts(brand)}>
                    <Card.Body className="brand-card-body">
                      <div className="brand-logo-container">
                        <img src={brand.logo_marque} alt={brand.nom_marque} className="brand-logo" />
                      </div>
                      <Card.Title className="brand-name">{brand.nom_marque}</Card.Title>
                    </Card.Body>
                  </Card>
                </Col>
              ))}
            </Row>
          ) : (
            <Alert variant="info" className="text-center">
              Aucune marque disponible
            </Alert>
          )}
        </Tab>

        {/* Products Tab */}
        <Tab eventKey="products" title="Produits" disabled={!selectedBrand}>
          {selectedBrand && (
            <>
              <div className="brand-header mb-4">
                <div className="brand-info">
                  <div className="brand-logo-title">
                    <div className="brand-logo-small">
                      <img src={selectedBrand.logo_marque} alt={selectedBrand.nom_marque} />
                    </div>
                    <h2 className="brand-title">{selectedBrand.nom_marque}</h2>
                  </div>
                  {selectedBrand.description_marque && <p className="brand-description">{selectedBrand.description_marque}</p>}
                </div>
                <Button variant="outline-primary" onClick={() => setActiveTab('brands')} className="back-button">
                  ← Retour aux marques
                </Button>
              </div>

              {/* Filters */}
              <div className="filters mb-4">
                <Row>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Catégorie</Form.Label>
                      <Form.Select
                        value={selectedCategory?.id || ''}
                        onChange={(e) => {
                          const catId = e.target.value;
                          setSelectedCategory(catId ? categories.find((c) => c.id === parseInt(catId)) : null);
                          setSelectedSubCategory(null);
                        }}
                        className="filter-select"
                      >
                        <option value="">Toutes les catégories</option>
                        {categories.map((category) => (
                          <option key={category.id} value={category.id}>
                            {category.nom}
                          </option>
                        ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Sous-catégorie</Form.Label>
                      <Form.Select
                        value={selectedSubCategory?.id || ''}
                        onChange={(e) => {
                          const subCatId = e.target.value;
                          setSelectedSubCategory(subCatId ? subCategories.find((sc) => sc.id === parseInt(subCatId)) : null);
                        }}
                        disabled={!selectedCategory}
                        className="filter-select"
                      >
                        <option value="">Toutes les sous-catégories</option>
                        {selectedCategory &&
                          subCategories
                            .filter((sc) => sc.categorie_id === selectedCategory.id)
                            .map((subCategory) => (
                              <option key={subCategory.id} value={subCategory.id}>
                                {subCategory.nom}
                              </option>
                            ))}
                      </Form.Select>
                    </Form.Group>
                  </Col>
                  <Col md={4}>
                    <Form.Group>
                      <Form.Label>Recherche</Form.Label>
                      <Form.Control
                        type="text"
                        placeholder="Rechercher par nom ou référence..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="search-input"
                      />
                    </Form.Group>
                  </Col>
                </Row>
                <div className="results-count">{filteredProducts.length} produit(s) trouvé(s)</div>
              </div>

              {renderPagination()}

              {loading.products ? (
                <div className="text-center py-5">
                  <Spinner animation="border" variant="primary" />
                </div>
              ) : filteredProducts.length > 0 ? (
                <>
                  <Row xs={1} md={2} lg={3} xl={4} className="g-4 mb-4">
                    {currentProducts.map((product) => (
                      <Col key={product.id}>{renderProductCard(product)}</Col>
                    ))}
                  </Row>
                  {renderPagination()}
                </>
              ) : (
                <Alert variant="info" className="no-results">
                  Aucun produit trouvé correspondant à votre recherche
                </Alert>
              )}
            </>
          )}
        </Tab>
      </Tabs>

      {/* Product Modal */}
      {/* Edit Produit Modal */}
      {showEditProduit && editProduitData && (
        <EditProduit
          product={editProduitData}
          onClose={() => {
            setShowEditProduit(false);
            setEditProduitData(null);
          }}
          onSuccess={handleEditProduitSuccess}
        />
      )}

      <Modal
        show={showProductModal}
        onHide={() => {
          setShowProductModal(false);
          setEditMode(false);
        }}
        size="lg"
        centered
        className="product-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title>{editMode ? 'Modifier Produit' : 'Détails du Produit'}</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {loading.productDetails ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
            </div>
          ) : (
            <>
              {editMode ? (
                <Form>
                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Image URL</Form.Label>
                        <Form.Control
                          type="text"
                          name="image_produit"
                          value={productForm.image_produit}
                          onChange={handleFormChange}
                          disabled={loading.saving}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Nom du produit</Form.Label>
                        <Form.Control
                          type="text"
                          name="nom_produit"
                          value={productForm.nom_produit}
                          onChange={handleFormChange}
                          disabled={loading.saving}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Row>
                    <Col md={6}>
                      <Form.Group className="mb-3">
                        <Form.Label>Référence</Form.Label>
                        <Form.Control
                          type="text"
                          name="reference_produit"
                          value={productForm.reference_produit}
                          onChange={handleFormChange}
                          disabled={loading.saving}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group className="mb-3">
                        <Form.Label>Prix (€)</Form.Label>
                        <Form.Control
                          type="text"
                          name="prix_produit"
                          value={productForm.prix_produit}
                          onChange={handleFormChange}
                          disabled={loading.saving}
                        />
                      </Form.Group>
                    </Col>
                    <Col md={3}>
                      <Form.Group className="mb-3">
                        <Form.Label>Quantité</Form.Label>
                        <Form.Control
                          type="number"
                          name="quantite_produit"
                          value={productForm.quantite_produit}
                          onChange={handleFormChange}
                          disabled={loading.saving}
                        />
                      </Form.Group>
                    </Col>
                  </Row>

                  <Form.Group className="mb-3">
                    <Form.Label>Description</Form.Label>
                    <Form.Control
                      as="textarea"
                      rows={3}
                      name="description_produit"
                      value={productForm.description_produit}
                      onChange={handleFormChange}
                      disabled={loading.saving}
                    />
                  </Form.Group>
                </Form>
              ) : (
                <Row>
                  <Col md={6}>
                    <div className="modal-product-image">
                      <img src={selectedProduct?.image_produit || 'https://via.placeholder.com/300'} alt={selectedProduct?.nom_produit} />
                    </div>
                  </Col>
                  <Col md={6}>
                    <h3 className="modal-product-title">{selectedProduct?.nom_produit}</h3>
                    <p className="modal-product-reference">Réf: {selectedProduct?.reference_produit || 'N/A'}</p>

                    {selectedProduct?.sousSousCategorie?.sousCategorie?.categorie && (
                      <div className="modal-product-categories mb-3">
                        <h5>Catégories</h5>
                        <div>
                          <Badge bg="light" text="dark" className="me-2">
                            {selectedProduct.sousSousCategorie.sousCategorie.categorie.nom}
                          </Badge>
                          <Badge bg="light" text="dark" className="me-2">
                            {selectedProduct.sousSousCategorie.sousCategorie.nom}
                          </Badge>
                          <Badge bg="light" text="dark">
                            {selectedProduct.sousSousCategorie.nom}
                          </Badge>
                        </div>
                      </div>
                    )}

                    <div className="modal-product-price mb-3">
                      <h5>{selectedProduct?.prix_produit ? `${selectedProduct.prix_produit} €` : 'Prix sur demande'}</h5>
                      <span className={`stock-badge ${selectedProduct?.quantite_produit > 0 ? 'in-stock' : 'out-of-stock'}`}>
                        {selectedProduct?.quantite_produit > 0 ? 'En stock' : 'Rupture'}
                      </span>
                    </div>

                    {selectedProduct?.description_produit && (
                      <div className="modal-product-description">
                        <h5>Description</h5>
                        <p>{selectedProduct.description_produit}</p>
                      </div>
                    )}
                  </Col>
                </Row>
              )}
            </>
          )}
        </Modal.Body>
        <Modal.Footer>
          {editMode ? (
            <>
              <Button variant="secondary" onClick={() => setEditMode(false)} disabled={loading.saving}>
                Annuler
              </Button>
              <Button variant="primary" onClick={handleSaveChanges} disabled={loading.saving}>
                {loading.saving ? (
                  <>
                    <Spinner as="span" size="sm" animation="border" role="status" />
                    <span className="ms-2">Enregistrement...</span>
                  </>
                ) : (
                  'Enregistrer'
                )}
              </Button>
            </>
          ) : (
            <>
              <Button variant="secondary" onClick={() => setShowProductModal(false)}>
                Fermer
              </Button>
              <Button variant="primary" onClick={handleEditClick}>
                Modifier
              </Button>
            </>
          )}
        </Modal.Footer>
      </Modal>
      <style>{`
        .catalog-container {
          max-width: 1200px;
        }
        .catalog-header {
          position: relative;
        }
        .catalog-title {
          font-size: 2.2rem;
          font-weight: 600;
          color: ${colors.primaryDark};
          letter-spacing: 1px;
          margin-bottom: 1rem;
        }
        .catalog-divider {
          height: 3px;
          width: 120px;
          background: linear-gradient(90deg, rgba(58,83,155,0.2) 0%, rgba(58,83,155,1) 50%, rgba(58,83,155,0.2) 100%);
          border-radius: 3px;
          margin: 0 auto;
        }
        .brand-card {
          border-radius: 12px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
          border: none;
        }
        .brand-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .brand-card-body {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 1.5rem;
        }
        .brand-logo-container {
          width: 100px;
          height: 100px;
          background-color: ${colors.lightGray};
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 1rem;
        }
        .brand-logo {
          max-height: 80%;
          max-width: 80%;
          object-fit: contain;
        }
        .brand-name {
          color: ${colors.primaryDark};
          font-weight: 600;
          text-align: center;
        }
        .brand-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 2rem;
        }
        .brand-info {
          flex: 1;
        }
        .brand-logo-title {
          display: flex;
          align-items: center;
          margin-bottom: 0.5rem;
        }
        .brand-logo-small {
          width: 60px;
          height: 60px;
          background-color: ${colors.lightGray};
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 1rem;
        }
        .brand-logo-small img {
          max-height: 80%;
          max-width: 80%;
          object-fit: contain;
        }
        .brand-title {
          font-size: 1.5rem;
          font-weight: 600;
          color: ${colors.primaryDark};
          margin: 0;
        }
        .brand-description {
          color: #6c757d;
          max-width: 800px;
          line-height: 1.5;
          margin: 0;
        }
        .back-button {
          border-width: 2px;
          font-weight: 500;
          margin-left: 1rem;
          white-space: nowrap;
        }
        .filters {
          background-color: white;
          padding: 1.5rem;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.05);
          margin-bottom: 2rem;
        }
        .filter-select, .search-input {
          border-radius: 20px;
          padding: 10px 20px;
          width: 100%;
        }
        .results-count {
          color: #6c757d;
          font-size: 0.875rem;
          margin-top: 0.5rem;
        }
        .product-card {
          border-radius: 12px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
          height: 100%;
          border: none;
        }
        .product-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .product-image-container {
          height: 200px;
          padding: 1rem;
          background-color: ${colors.lightGray};
          display: flex;
          align-items: center;
          border-radius: 12px 12px 0 0;
        }
        .product-image {
          max-height: 100%;
          max-width: 100%;
          object-fit: contain;
          margin: 0 auto;
        }
        .product-title {
          font-size: 1rem;
          font-weight: 600;
          color: ${colors.primaryLight};
          margin-bottom: 0.5rem;
        }
        .product-reference {
          color: #6c757d;
          font-size: 0.85rem;
          margin-bottom: 0.75rem;
        }
        .product-categories {
          margin-bottom: 0.75rem;
        }
        .product-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: auto;
        }
        .product-price {
          color: ${colors.accent};
          font-weight: bold;
          font-size: 1.1rem;
        }
        .product-stock {
        }
        .modal-product-categories {
          margin-bottom: 1rem;
        }
        .modal-product-price {
          display: flex;
          align-items: center;
          margin-bottom: 1.5rem;
        }
        .modal-product-price h5 {
          color: ${colors.accent};
          margin: 0;
          margin-right: 1rem;
        }
        .stock-badge {
          font-size: 0.875rem;
          padding: 0.35em 0.65em;
          border-radius: 50rem;
        }
        .modal-product-description h5 {
          color: ${colors.primaryDark};
          margin-bottom: 0.5rem;
        }
        .modal-product-description p {
          color: #495057;
          line-height: 1.6;
        }
      `}</style>
    </Container>
  );
};

export default BrandCatalog;
