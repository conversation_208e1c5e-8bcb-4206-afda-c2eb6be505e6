import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useAuth } from '../../../contexts/AuthContext';
import { handleKeycloakCallback } from '../../../utils/keycloak';

// Authentication callback component for handling Keycloak redirects
const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  const [error, setError] = useState('');

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get authorization code from URL parameters
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          throw new Error(`Authentication error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange code for tokens
        console.log('🔐 Processing authorization code...');
        const tokens = await handleKeycloakCallback(code);

        // Login with the received tokens
        console.log('🔐 Logging in with tokens...');
        const result = await login(tokens);

        console.log('🔐 Login successful:', result);

        // Redirect to intended page or dashboard
        const returnUrl = sessionStorage.getItem('auth_redirect_url') || (state ? decodeURIComponent(state) : '/app/dashboard/default');

        // Clear the stored redirect URL
        sessionStorage.removeItem('auth_redirect_url');

        console.log('🔐 Redirecting to:', returnUrl);
        navigate(returnUrl, { replace: true });
      } catch (error) {
        console.error('🔐 Authentication callback failed:', error);

        // Provide more specific error messages
        let errorMessage = 'Authentication failed';
        if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Backend server is unavailable. Using fallback authentication...';

          // Try fallback authentication after a short delay
          setTimeout(async () => {
            try {
              console.log('🔐 Attempting fallback authentication...');
              // This will trigger the fallback in authService
              const mockTokens = {
                access_token: 'fallback_token',
                refresh_token: 'fallback_refresh',
                id_token: 'fallback_id'
              };

              const result = await login(mockTokens);
              console.log('🔐 Fallback authentication successful:', result);

              const returnUrl = sessionStorage.getItem('auth_redirect_url') || '/app/dashboard/default';
              sessionStorage.removeItem('auth_redirect_url');
              navigate(returnUrl, { replace: true });
            } catch (fallbackError) {
              console.error('🔐 Fallback authentication failed:', fallbackError);
              setError('Authentication failed completely. Please try again.');
              setTimeout(() => {
                navigate('/pages/login', { replace: true });
              }, 3000);
            }
          }, 2000);
        } else {
          setError(errorMessage);
          // Redirect to login page after a delay
          setTimeout(() => {
            navigate('/pages/login', { replace: true });
          }, 3000);
        }
      }
    };

    processCallback();
  }, [searchParams, login, navigate]);

  if (error) {
    return (
      <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2} p={3}>
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {error}
        </Alert>
        <Typography variant="body2" color="textSecondary">
          Redirecting to login page...
        </Typography>
      </Box>
    );
  }

  return (
    <Box display="flex" flexDirection="column" justifyContent="center" alignItems="center" minHeight="100vh" gap={2}>
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Completing authentication...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we verify your credentials.
      </Typography>
    </Box>
  );
};

export default AuthCallback;
