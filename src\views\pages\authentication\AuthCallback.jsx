import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import { useAuth } from '../../../contexts/AuthContext';
import { handleKeycloakCallback } from '../../../utils/keycloak';

// Authentication callback component for handling Keycloak redirects
const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login } = useAuth();
  const [error, setError] = useState('');

  useEffect(() => {
    const processCallback = async () => {
      try {
        // Get authorization code from URL parameters
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        if (error) {
          throw new Error(`Authentication error: ${error}`);
        }

        if (!code) {
          throw new Error('No authorization code received');
        }

        // Exchange code for tokens
        const tokens = await handleKeycloakCallback(code);
        
        // Login with the received tokens
        await login(tokens);
        
        // Redirect to intended page or dashboard
        const returnUrl = state ? decodeURIComponent(state) : '/dashboard/default';
        navigate(returnUrl, { replace: true });
        
      } catch (error) {
        console.error('Authentication callback failed:', error);
        setError(error.message || 'Authentication failed');
        
        // Redirect to login page after a delay
        setTimeout(() => {
          navigate('/pages/login', { replace: true });
        }, 3000);
      }
    };

    processCallback();
  }, [searchParams, login, navigate]);

  if (error) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
        gap={2}
        p={3}
      >
        <Alert severity="error" sx={{ maxWidth: 400 }}>
          {error}
        </Alert>
        <Typography variant="body2" color="textSecondary">
          Redirecting to login page...
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      minHeight="100vh"
      gap={2}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        Completing authentication...
      </Typography>
      <Typography variant="body2" color="textSecondary">
        Please wait while we verify your credentials.
      </Typography>
    </Box>
  );
};

export default AuthCallback;
