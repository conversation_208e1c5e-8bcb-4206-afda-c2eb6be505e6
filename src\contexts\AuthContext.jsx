import React, { createContext, useContext, useReducer, useEffect } from 'react';
import authService from '../services/authService';

// Initial state
const initialState = {
  isAuthenticated: false,
  isLoading: true,
  user: null,
  roles: [],
  error: null
};

// Action types
const AUTH_ACTIONS = {
  LOGIN_START: 'LOGIN_START',
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGIN_FAILURE: 'LOGIN_FAILURE',
  LOGOUT: 'LOGOUT',
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  CLEAR_ERROR: 'CLEAR_ERROR'
};

// Reducer
function authReducer(state, action) {
  switch (action.type) {
    case AUTH_ACTIONS.LOGIN_START:
      return {
        ...state,
        isLoading: true,
        error: null
      };

    case AUTH_ACTIONS.LOGIN_SUCCESS:
      return {
        ...state,
        isAuthenticated: true,
        isLoading: false,
        user: action.payload.user,
        roles: action.payload.roles || [],
        error: null
      };

    case AUTH_ACTIONS.LOGIN_FAILURE:
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        roles: [],
        error: action.payload.error
      };

    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        isAuthenticated: false,
        isLoading: false,
        user: null,
        roles: [],
        error: null
      };

    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        isLoading: action.payload
      };

    case AUTH_ACTIONS.SET_USER:
      return {
        ...state,
        user: action.payload.user,
        roles: action.payload.roles || [],
        isAuthenticated: true
      };

    case AUTH_ACTIONS.CLEAR_ERROR:
      return {
        ...state,
        error: null
      };

    default:
      return state;
  }
}

// Create context
const AuthContext = createContext();

// Auth Provider component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Logout function (defined early to avoid circular dependency)
  const logout = async () => {
    try {
      await authService.logout();
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    } catch (error) {
      console.error('Logout failed:', error);
      // Still dispatch logout to clear local state
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
    }
  };

  // Initialize authentication state on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        console.log('🔐 Initializing authentication...');
        dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

        // Check if user is already authenticated
        const isAuth = authService.isAuthenticated();
        console.log('🔐 Is authenticated:', isAuth);

        // Debug: Show what tokens are stored
        const storedTokens = {
          access: !!authService.getAccessToken(),
          refresh: !!authService.getRefreshToken(),
          id: !!authService.getIdToken()
        };
        console.log('🔐 Stored tokens:', storedTokens);

        if (isAuth) {
          const user = authService.getUser();
          const roles = authService.getUserRoles();
          console.log('🔐 Found existing user:', user);
          console.log('🔐 User roles:', roles);

          // Check if token is expired
          if (authService.isTokenExpired()) {
            console.log('🔐 Token expired, attempting refresh...');
            try {
              // Try to refresh token
              await authService.refreshToken();
              const updatedUser = authService.getUser();
              const updatedRoles = authService.getUserRoles();

              console.log('🔐 Token refreshed successfully');
              dispatch({
                type: AUTH_ACTIONS.LOGIN_SUCCESS,
                payload: { user: updatedUser, roles: updatedRoles }
              });
            } catch (error) {
              // Refresh failed, logout user
              console.log('🔐 Token refresh failed, logging out user');
              await logout();
            }
          } else {
            // Token is still valid
            console.log('🔐 Token still valid, logging in user');
            dispatch({
              type: AUTH_ACTIONS.LOGIN_SUCCESS,
              payload: { user, roles }
            });
          }
        } else {
          // User is not authenticated
          console.log('🔐 User not authenticated, setting loading to false');
          dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
        }
      } catch (error) {
        console.error('🔐 Auth initialization failed:', error);
        dispatch({
          type: AUTH_ACTIONS.LOGIN_FAILURE,
          payload: { error: 'Failed to initialize authentication' }
        });
      }
    };

    initializeAuth();
  }, []);

  // Login function
  const login = async (tokens) => {
    try {
      dispatch({ type: AUTH_ACTIONS.LOGIN_START });

      const result = await authService.verifyToken(tokens);

      dispatch({
        type: AUTH_ACTIONS.LOGIN_SUCCESS,
        payload: {
          user: result.user,
          roles: result.roles
        }
      });

      return result;
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.LOGIN_FAILURE,
        payload: { error: error.message }
      });
      throw error;
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    try {
      const user = await authService.getCurrentUser();
      const roles = authService.getUserRoles();

      dispatch({
        type: AUTH_ACTIONS.SET_USER,
        payload: { user, roles }
      });

      return user;
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      throw error;
    }
  };

  // Clear error
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.CLEAR_ERROR });
  };

  // Check if user has specific role
  const hasRole = (role) => {
    return state.roles.includes(role);
  };

  // Check if user has any of the specified roles
  const hasAnyRole = (roles) => {
    return roles.some((role) => state.roles.includes(role));
  };

  const value = {
    ...state,
    login,
    logout,
    refreshUser,
    clearError,
    hasRole,
    hasAnyRole
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

export default AuthContext;
