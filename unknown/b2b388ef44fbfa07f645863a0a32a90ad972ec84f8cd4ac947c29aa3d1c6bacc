# Gestion des Partenaires

## Introduction

Le système permet de gérer des partenaires commerciaux qui bénéficient de remises spéciales. Un partenaire est un utilisateur avec le rôle "partenaire" et des informations supplémentaires comme un taux de remise spécifique.

## Endpoints API

### Récupérer tous les partenaires

```
GET /api/partenaires
```

Retourne la liste de tous les partenaires.

#### Exemple de réponse

```json
[
  {
    "id": 1,
    "name": "Entreprise ABC",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "point_de_vente_id": null,
    "groupe_client_id": null,
    "remise_personnelle": 0,
    "type_client": "partenaire",
    "partenaire": {
      "id": 1,
      "user_id": 1,
      "remise": 15.00,
      "description": "Partenaire premium",
      "statut": "actif",
      "created_at": "2025-04-01T10:00:00.000000Z",
      "updated_at": "2025-04-01T10:00:00.000000Z"
    }
  },
  {
    "id": 2,
    "name": "Société XYZ",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "point_de_vente_id": null,
    "groupe_client_id": null,
    "remise_personnelle": 0,
    "type_client": "partenaire",
    "partenaire": {
      "id": 2,
      "user_id": 2,
      "remise": 12.50,
      "description": "Partenaire standard",
      "statut": "actif",
      "created_at": "2025-04-02T11:30:00.000000Z",
      "updated_at": "2025-04-02T11:30:00.000000Z"
    }
  }
]
```

### Récupérer un partenaire spécifique

```
GET /api/partenaires/{id}
```

Retourne les détails d'un partenaire spécifique.

#### Exemple de réponse

```json
{
  "id": 1,
  "user_id": 1,
  "remise": 15.00,
  "description": "Partenaire premium",
  "statut": "actif",
  "created_at": "2025-04-01T10:00:00.000000Z",
  "updated_at": "2025-04-01T10:00:00.000000Z",
  "user": {
    "id": 1,
    "name": "Entreprise ABC",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "point_de_vente_id": null,
    "groupe_client_id": null,
    "remise_personnelle": 0,
    "type_client": "partenaire"
  }
}
```

### Créer un nouveau partenaire

```
POST /api/partenaires
```

Crée un nouveau partenaire en associant un utilisateur existant.

#### Paramètres de la requête

| Paramètre   | Type    | Description                                |
|-------------|---------|--------------------------------------------|
| user_id     | integer | ID de l'utilisateur à associer             |
| remise      | decimal | Pourcentage de remise (0-100)              |
| description | string  | Description du partenaire (optionnel)      |
| statut      | string  | Statut du partenaire (actif/inactif)       |

#### Exemple de requête

```json
{
  "user_id": 3,
  "remise": 10.00,
  "description": "Nouveau partenaire",
  "statut": "actif"
}
```

#### Exemple de réponse

```json
{
  "id": 3,
  "user_id": 3,
  "remise": 10.00,
  "description": "Nouveau partenaire",
  "statut": "actif",
  "created_at": "2025-04-04T14:20:00.000000Z",
  "updated_at": "2025-04-04T14:20:00.000000Z",
  "user": {
    "id": 3,
    "name": "Entreprise DEF",
    "email": "<EMAIL>",
    "roles": ["client", "partenaire"],
    "type_client": "partenaire"
  }
}
```

### Mettre à jour un partenaire

```
PUT /api/partenaires/{id}
```

Met à jour les informations d'un partenaire existant.

#### Paramètres de la requête

| Paramètre   | Type    | Description                                |
|-------------|---------|--------------------------------------------|
| remise      | decimal | Pourcentage de remise (0-100)              |
| description | string  | Description du partenaire                  |
| statut      | string  | Statut du partenaire (actif/inactif)       |

#### Exemple de requête

```json
{
  "remise": 18.00,
  "description": "Partenaire premium mis à jour",
  "statut": "actif"
}
```

### Supprimer un partenaire

```
DELETE /api/partenaires/{id}
```

Supprime un partenaire existant. Cette action ne supprime pas l'utilisateur associé, mais retire simplement son statut de partenaire.

## Logique de remise

Lorsqu'un utilisateur est défini comme partenaire:

1. Son type_client est défini sur "partenaire"
2. Le rôle "partenaire" est ajouté à ses rôles
3. Une entrée est créée dans la table "partenaires" avec le taux de remise spécifié

Lors du calcul de la remise effective pour un client:

1. Si le client a une remise personnelle, celle-ci est utilisée en priorité
2. Sinon, si le client est de type "partenaire", la remise définie dans la table "partenaires" est utilisée

Cette remise est ensuite appliquée automatiquement lors de la création de commandes par ce client, sauf si une remise spécifique à la commande est définie et est plus avantageuse.

## Changement de type de client

Pour changer un client normal en partenaire, vous pouvez utiliser l'endpoint suivant:

```
PUT /api/clients/{id}/type
```

#### Exemple de requête

```json
{
  "type_client": "partenaire"
}
```

Cette action créera automatiquement une entrée partenaire avec une remise par défaut de 0%. Vous pourrez ensuite mettre à jour cette remise via l'endpoint `PUT /api/partenaires/{id}`.
